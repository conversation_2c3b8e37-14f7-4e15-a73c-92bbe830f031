use solana_sdk::signature::{Keypair, Signer};
use std::time::{Instant, Duration};
use std::sync::{Arc, atomic::{AtomicU64, AtomicBool, Ordering}};
use std::thread;
use std::io::{self, Write};
use rayon::prelude::*;

// Configuration constants - optimized for M1 Pro
const CASE_SENSITIVE: bool = true;
const BATCH_SIZE: usize = 50_000; // Smaller batches for better cache locality
const REPORT_THRESHOLD: u64 = 50_000;

#[derive(Debug, <PERSON>lone)]
enum MatchPosition {
    StartOnly,
    EndOnly,
    StartOrEnd,
    Anywhere,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
struct PatternData {
    pattern: String,
    pattern_bytes: Vec<u8>,
    length: usize,
}

impl PatternData {
    fn new(pattern: &str) -> Self {
        Self {
            pattern: pattern.to_string(),
            pattern_bytes: pattern.as_bytes().to_vec(),
            length: pattern.len(),
        }
    }
}

// Helper function to format large numbers
fn format_number(num: f64) -> String {
    if num >= 1_000_000_000.0 {
        format!("{:.2}B", num / 1_000_000_000.0)
    } else if num >= 1_000_000.0 {
        format!("{:.2}M", num / 1_000_000.0)
    } else if num >= 1_000.0 {
        format!("{:.2}K", num / 1_000.0)
    } else {
        format!("{:.0}", num)
    }
}

fn validate_prefix(prefix: &str) -> bool {
    let base58_alphabet = "123456789ABCDEFGHIJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";
    prefix.chars().all(|c| base58_alphabet.contains(c))
}

// Optimized address checking using raw bytes
fn check_address_bytes(address_bytes: &[u8], pattern: &PatternData, match_position: &MatchPosition) -> Option<(String, String)> {
    let pattern_bytes = &pattern.pattern_bytes;
    
    match match_position {
        MatchPosition::StartOnly => {
            if address_bytes.len() >= pattern_bytes.len() && 
               address_bytes[..pattern_bytes.len()] == *pattern_bytes {
                let actual_match = String::from_utf8_lossy(&address_bytes[..pattern_bytes.len()]).to_string();
                Some(("start".to_string(), actual_match))
            } else {
                None
            }
        }
        MatchPosition::EndOnly => {
            if address_bytes.len() >= pattern_bytes.len() {
                let start_idx = address_bytes.len() - pattern_bytes.len();
                if address_bytes[start_idx..] == *pattern_bytes {
                    let actual_match = String::from_utf8_lossy(&address_bytes[start_idx..]).to_string();
                    Some(("end".to_string(), actual_match))
                } else {
                    None
                }
            } else {
                None
            }
        }
        MatchPosition::StartOrEnd => {
            // Check start
            if address_bytes.len() >= pattern_bytes.len() && 
               address_bytes[..pattern_bytes.len()] == *pattern_bytes {
                let actual_match = String::from_utf8_lossy(&address_bytes[..pattern_bytes.len()]).to_string();
                return Some(("start".to_string(), actual_match));
            }
            // Check end
            if address_bytes.len() >= pattern_bytes.len() {
                let start_idx = address_bytes.len() - pattern_bytes.len();
                if address_bytes[start_idx..] == *pattern_bytes {
                    let actual_match = String::from_utf8_lossy(&address_bytes[start_idx..]).to_string();
                    return Some(("end".to_string(), actual_match));
                }
            }
            None
        }
        MatchPosition::Anywhere => {
            if let Some(pos) = address_bytes.windows(pattern_bytes.len())
                .position(|window| window == pattern_bytes) {
                let actual_match = String::from_utf8_lossy(&address_bytes[pos..pos + pattern_bytes.len()]).to_string();
                Some((format!("index {}", pos), actual_match))
            } else {
                None
            }
        }
    }
}

// Optimized keypair generation with batch processing
fn generate_and_check_batch(
    patterns: &[PatternData],
    match_position: &MatchPosition,
    batch_size: usize
) -> Option<(String, String, String, String, String)> {

    // Pre-allocate buffers to reduce allocations
    let mut address_buffer = Vec::with_capacity(44); // Max base58 address length

    for _ in 0..batch_size {
        // Generate keypair using the default method
        let keypair = Keypair::new();
        let pubkey = keypair.pubkey();

        // Convert to base58 bytes directly
        address_buffer.clear();
        let address_str = pubkey.to_string();
        address_buffer.extend_from_slice(address_str.as_bytes());

        // Check against all patterns using byte comparison
        for pattern in patterns {
            if let Some((position, actual_match)) = check_address_bytes(&address_buffer, pattern, match_position) {
                let private_key = bs58::encode(keypair.to_bytes()).into_string();
                return Some((
                    pattern.pattern.clone(),
                    position,
                    actual_match,
                    address_str,
                    private_key
                ));
            }
        }
    }

    None
}

fn main() {
    // Define possible patterns we want to match
    let possible_patterns = vec!["Artin", "ARTIN"];
    let match_position = MatchPosition::StartOrEnd;
    
    let start_time = Instant::now();
    let attempts = Arc::new(AtomicU64::new(0));
    let found = Arc::new(AtomicBool::new(false));
    let found_keypair = Arc::new(parking_lot::Mutex::new(None::<(String, String, String, String, String)>));

    // Validate all patterns
    for pattern in &possible_patterns {
        if !validate_prefix(pattern) {
            eprintln!("Error: Pattern \"{}\" contains invalid Base58 characters", pattern);
            eprintln!("Valid characters are: 123456789ABCDEFGHIJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz");
            std::process::exit(1);
        }
    }

    // Pre-compute pattern data
    let pattern_data: Vec<PatternData> = possible_patterns.iter()
        .map(|p| PatternData::new(p))
        .collect();

    println!("Searching for Solana address matching any of these patterns: {}", 
        possible_patterns.join(", "));
    println!("Match position: {:?}", match_position);
    println!("Case sensitive: {}", CASE_SENSITIVE);
    println!("Using {} CPU threads with optimized batch processing", num_cpus::get());

    // Progress reporting thread
    let attempts_clone = Arc::clone(&attempts);
    let found_clone = Arc::clone(&found);
    let start_time_clone = start_time;
    thread::spawn(move || {
        let mut last_report_time = start_time_clone;
        let mut last_report_attempts = 0u64;
        
        while !found_clone.load(Ordering::Relaxed) {
            thread::sleep(Duration::from_secs(1));
            let now = Instant::now();
            let elapsed = now.duration_since(start_time_clone).as_secs_f64();
            let total_attempts = attempts_clone.load(Ordering::Relaxed);
            
            let time_span = now.duration_since(last_report_time).as_secs_f64();
            let recent_attempts = total_attempts - last_report_attempts;
            let current_rate = if time_span > 0.0 { recent_attempts as f64 / time_span } else { 0.0 };
            let avg_rate = if elapsed > 0.0 { total_attempts as f64 / elapsed } else { 0.0 };
            
            print!("\rAttempts: {} | Current Rate: {} addr/sec | Avg Rate: {} addr/sec | Running: {}s",
                format_number(total_attempts as f64),
                format_number(current_rate),
                format_number(avg_rate),
                elapsed as u64
            );
            io::stdout().flush().unwrap();
            
            last_report_time = now;
            last_report_attempts = total_attempts;
        }
    });

    // Parallel keypair generation with optimized batching
    (0..num_cpus::get()).into_par_iter().for_each(|_worker_id| {
        let mut local_attempts = 0u64;

        while !found.load(Ordering::Relaxed) {
            if let Some(result) = generate_and_check_batch(&pattern_data, &match_position, BATCH_SIZE) {
                found.store(true, Ordering::Relaxed);
                *found_keypair.lock() = Some(result);
                attempts.fetch_add(local_attempts + BATCH_SIZE as u64, Ordering::Relaxed);
                return;
            }
            
            local_attempts += BATCH_SIZE as u64;
            
            // Report progress periodically
            if local_attempts % REPORT_THRESHOLD == 0 {
                attempts.fetch_add(REPORT_THRESHOLD, Ordering::Relaxed);
                local_attempts = 0;
            }
        }
        
        // Report remaining attempts
        if local_attempts > 0 {
            attempts.fetch_add(local_attempts, Ordering::Relaxed);
        }
    });

    let result = found_keypair.lock().clone();
    if let Some((matched_pattern, position, actual_match, pubkey, private_key)) = result {
        let elapsed_time = start_time.elapsed().as_secs_f64();
        let total_attempts = attempts.load(Ordering::Relaxed);
        
        println!("\n\nFound matching address!");
        println!("Matched pattern: {}", matched_pattern);
        println!("Match position: {}", position);
        println!("Actual match: {}", actual_match);
        println!("Public Key: {}", pubkey);
        println!("Secret Key: {}", private_key);
        println!("Total attempts: {}", total_attempts);
        println!("Time taken: {:.2} seconds", elapsed_time);
        println!("Speed: {} addresses/second", format_number(total_attempts as f64 / elapsed_time));
    }
}
