{"rustc": 4723136837156968084, "features": "[\"default\"]", "declared_features": "[\"asm\", \"default\", \"parallel\", \"rayon\", \"std\"]", "target": 4360302069253712615, "profile": 6418093726722172150, "path": 5944131557564890203, "deps": [[477150410136574819, "ark_ff_macros", false, 1700200388091507292], [2932480923465029663, "zeroize", false, 1134305864737283157], [5157631553186200874, "num_traits", false, 1555090638477747650], [11903278875415370753, "itertools", false, 18314660748751123816], [12528732512569713347, "num_bigint", false, 1758634574806585994], [13859769749131231458, "derivative", false, 2049394316149795230], [15179503056858879355, "ark_std", false, 13473159868556679021], [16925068697324277505, "ark_serialize", false, 15399164065715722127], [17475753849556516473, "digest", false, 12462944596758972673], [17605717126308396068, "paste", false, 9140080941227406887], [17996237327373919127, "ark_ff_asm", false, 1108552847853514293]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/ark-ff-1ee70bdf5e15ace5/dep-lib-ark_ff", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}