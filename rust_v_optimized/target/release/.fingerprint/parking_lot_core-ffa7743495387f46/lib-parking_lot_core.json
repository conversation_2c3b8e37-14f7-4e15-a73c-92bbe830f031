{"rustc": 4723136837156968084, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 12558056885032795287, "profile": 16503403049695105087, "path": 1371685602149481821, "deps": [[2828590642173593838, "cfg_if", false, 16841781915602527215], [3666196340704888985, "smallvec", false, 12348891295046320301], [4269498962362888130, "build_script_build", false, 15458946321711078350], [4684437522915235464, "libc", false, 10902910220991786947]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/parking_lot_core-ffa7743495387f46/dep-lib-parking_lot_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}