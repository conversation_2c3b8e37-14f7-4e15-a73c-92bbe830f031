{"rustc": 4723136837156968084, "features": "[\"default\", \"hmac\", \"hmac-drbg\", \"sha2\", \"static-context\", \"std\", \"typenum\"]", "declared_features": "[\"default\", \"hmac\", \"hmac-drbg\", \"lazy-static-context\", \"lazy_static\", \"sha2\", \"static-context\", \"std\", \"typenum\"]", "target": 3229137391415082075, "profile": 6418093726722172150, "path": 7713849707038864110, "deps": [[326483822194815791, "hmac_drbg", false, 3259199522667665161], [4731167174326621189, "rand", false, 3876995238466890083], [6374421995994392543, "digest", false, 5611740324346735376], [9529943735784919782, "arrayref", false, 12957111590604592075], [9689903380558560274, "serde", false, 11261301935996946252], [10697153736615144157, "build_script_build", false, 8377469845000129545], [11472355562936271783, "sha2", false, 17806532756551386177], [13443824959912985638, "libsecp256k1_core", false, 5645895728351462048], [17001665395952474378, "typenum", false, 13733781105021171171], [17072468807347166763, "base64", false, 13288362731290269178]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/libsecp256k1-9ebaf47cf9c50b97/dep-lib-libsecp256k1", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}