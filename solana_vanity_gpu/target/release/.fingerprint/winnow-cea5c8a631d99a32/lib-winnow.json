{"rustc": 4723136837156968084, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 2315828811728255584, "path": 10360558576442246253, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/winnow-cea5c8a631d99a32/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}