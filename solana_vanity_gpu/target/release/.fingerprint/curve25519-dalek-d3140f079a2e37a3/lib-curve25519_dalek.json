{"rustc": 4723136837156968084, "features": "[\"alloc\", \"default\", \"serde\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 4744499769514376500, "profile": 6418093726722172150, "path": 4277333144663047180, "deps": [[1740877332521282793, "rand_core", false, 2151513396798926002], [2932480923465029663, "zeroize", false, 1134305864737283157], [3712811570531045576, "byteorder", false, 7729625386954667301], [6374421995994392543, "digest", false, 5611740324346735376], [9689903380558560274, "serde", false, 11261301935996946252], [17003143334332120809, "subtle", false, 5842945822738907064]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/curve25519-dalek-d3140f079a2e37a3/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}