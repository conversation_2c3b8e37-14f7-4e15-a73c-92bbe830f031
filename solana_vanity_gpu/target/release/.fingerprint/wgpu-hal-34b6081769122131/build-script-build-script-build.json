{"rustc": 4723136837156968084, "features": "[\"bit-set\", \"block\", \"d3d12\", \"default\", \"dx12\", \"libloading\", \"link\", \"metal\", \"range-alloc\"]", "declared_features": "[\"android_system_properties\", \"ash\", \"bit-set\", \"block\", \"d3d12\", \"default\", \"device_lost_panic\", \"dx12\", \"dxc_shader_compiler\", \"fragile-send-sync-non-atomic-wasm\", \"gles\", \"glow\", \"glutin_wgl_sys\", \"gpu-alloc\", \"gpu-allocator\", \"gpu-descriptor\", \"hassle-rs\", \"internal_error_panic\", \"khronos-egl\", \"libloading\", \"link\", \"metal\", \"ndk-sys\", \"oom_panic\", \"range-alloc\", \"renderdoc\", \"renderdoc-sys\", \"smallvec\", \"vulkan\", \"windows_rs\"]", "target": 5408242616063297496, "profile": 17257705230225558938, "path": 14138876507232900343, "deps": [[13650835054453599687, "cfg_aliases", false, 17966721267382741423]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wgpu-hal-34b6081769122131/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}