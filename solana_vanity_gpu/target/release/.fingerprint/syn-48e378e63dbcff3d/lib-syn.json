{"rustc": 4723136837156968084, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 17257705230225558938, "path": 8826818789162987351, "deps": [[1988483478007900009, "unicode_ident", false, 16637497482365649301], [3060637413840920116, "proc_macro2", false, 8654979111247074264], [17990358020177143287, "quote", false, 3345825101821211705]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-48e378e63dbcff3d/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}