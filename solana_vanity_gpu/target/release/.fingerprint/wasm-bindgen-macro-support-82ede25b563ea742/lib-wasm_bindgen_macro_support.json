{"rustc": 4723136837156968084, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 2554159132106277380, "path": 15555266722212114060, "deps": [[3060637413840920116, "proc_macro2", false, 8654979111247074264], [4974441333307933176, "syn", false, 5533940753606354596], [14299170049494554845, "wasm_bindgen_shared", false, 17701857061938531345], [14372503175394433084, "wasm_bindgen_backend", false, 3068425035666937097], [17990358020177143287, "quote", false, 3345825101821211705]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-macro-support-82ede25b563ea742/dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}