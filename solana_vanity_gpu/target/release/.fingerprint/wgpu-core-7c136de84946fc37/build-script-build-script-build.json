{"rustc": 4723136837156968084, "features": "[\"default\", \"dx12\", \"link\", \"metal\", \"raw-window-handle\", \"wgsl\"]", "declared_features": "[\"api_log_info\", \"default\", \"dx12\", \"fragile-send-sync-non-atomic-wasm\", \"gles\", \"glsl\", \"link\", \"metal\", \"raw-window-handle\", \"renderdoc\", \"replay\", \"resource_log_info\", \"ron\", \"serde\", \"spirv\", \"strict_asserts\", \"trace\", \"vulkan\", \"wgsl\"]", "target": 5408242616063297496, "profile": 17257705230225558938, "path": 15146860763164100538, "deps": [[13650835054453599687, "cfg_aliases", false, 17966721267382741423]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wgpu-core-7c136de84946fc37/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}