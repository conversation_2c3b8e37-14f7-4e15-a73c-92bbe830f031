{"rustc": 4723136837156968084, "features": "[\"assert_matches\", \"byteorder\", \"chrono\", \"default\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\", \"solana-logger\"]", "declared_features": "[\"assert_matches\", \"byteorder\", \"chrono\", \"curve25519-dalek\", \"default\", \"dev-context-only-utils\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"program\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\", \"solana-logger\"]", "target": 12656250652134757477, "profile": 6418093726722172150, "path": 11500654221973114674, "deps": [[58942224022519477, "solana_program", false, 10999697068663145842], [65234016722529558, "bincode", false, 10846534789313039716], [424821569244661989, "solana_frozen_abi", false, 14089940587047287237], [757899038044743028, "serde_with", false, 3036599773670976700], [1230783206204459120, "urip<PERSON>e", false, 11067724837219581638], [3712811570531045576, "byteorder", false, 7729625386954667301], [4097734106057062256, "bs58", false, 13687185604312180044], [4258399515347749257, "pbkdf2", false, 13136406573848072177], [4413975316754495123, "build_script_build", false, 18094083646244663991], [4731167174326621189, "rand0_7", false, 3876995238466890083], [5092398082731730447, "derivation_path", false, 12268420215905364894], [5157631553186200874, "num_traits", false, 1555090638477747650], [5892263340167577622, "solana_logger", false, 15735768193757066514], [5986029879202738730, "log", false, 3843349770280637250], [6203123018298125816, "borsh", false, 12395608899122336452], [6511429716036861196, "bytemuck", false, 17984191917956405896], [6946689283190175495, "wasm_bindgen", false, 1746002488860368418], [7858942147296547339, "rustversion", false, 1167664442114679678], [7896293946984509699, "bitflags", false, 11546570919836223558], [8008191657135824715, "thiserror", false, 1150419361916048626], [8079500665534101559, "siphasher", false, 10098274046184050381], [9077477275112005502, "solana_sdk_macro", false, 11396875981027528436], [9209347893430674936, "hmac", false, 10606099586208848773], [9689903380558560274, "serde", false, 11261301935996946252], [9857275760291862238, "sha2", false, 13423836654414614519], [9897246384292347999, "chrono", false, 7409574977273452319], [10504454274054532777, "memmap2", false, 14455506079350168584], [10520923840501062997, "generic_array", false, 12536909619820372807], [10697153736615144157, "libsecp256k1", false, 2410932980140669973], [10889494155287625682, "serde_bytes", false, 18003345432989177617], [11017232866922121725, "sha3", false, 15505822060272997693], [11263754829263059703, "num_derive", false, 7930924817848145717], [11903278875415370753, "itertools", false, 18314660748751123816], [13024038960712206194, "qstring", false, 18177595640122549379], [13208667028893622512, "rand", false, 13890537951672160946], [14165672584825129001, "solana_frozen_abi_macro", false, 4428385786175638792], [15367738274754116744, "serde_json", false, 12319156874948826685], [15407337108592562583, "ed25519_dalek_bip32", false, 11508127262877370811], [15877171306855209770, "qualifier_attr", false, 15972630535606059476], [16257276029081467297, "serde_derive", false, 986718799179666374], [16712258961403650142, "num_enum", false, 2783055549352312357], [16912878040847476921, "assert_matches", false, 11001776118897151093], [17475753849556516473, "digest", false, 12462944596758972673], [17917672826516349275, "lazy_static", false, 2139054082405725281], [17987314850127689447, "ed25519_dalek", false, 8356034220770167555], [18066890886671768183, "base64", false, 801767773081047609]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/solana-sdk-b818d03cf92c0539/dep-lib-solana_sdk", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}