{"rustc": 4723136837156968084, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 6418093726722172150, "path": 8980821075064133169, "deps": [[555019317135488525, "regex_automata", false, 12786955474016669986], [2779309023524819297, "aho_corasick", false, 7330247628507292074], [9408802513701742484, "regex_syntax", false, 8388358197189840853], [15932120279885307830, "memchr", false, 16330251229401700532]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-c0e937ac7614d802/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}