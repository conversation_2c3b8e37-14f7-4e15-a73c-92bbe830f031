{"rustc": 4723136837156968084, "features": "[]", "declared_features": "[\"default\", \"optick\", \"procmacros\", \"profile-with-optick\", \"profile-with-puffin\", \"profile-with-superluminal\", \"profile-with-tracing\", \"profile-with-tracy\", \"profiling-procmacros\", \"puffin\", \"superluminal-perf\", \"tracing\", \"tracy-client\", \"type-check\"]", "target": 1764792426699693407, "profile": 16503403049695105087, "path": 2844003550427123856, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/profiling-085731c60a9fd523/dep-lib-profiling", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}