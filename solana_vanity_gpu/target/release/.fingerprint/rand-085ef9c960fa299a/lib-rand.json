{"rustc": 4723136837156968084, "features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 6418093726722172150, "path": 14738424696537004185, "deps": [[1333041802001714747, "rand_chacha", false, 5405723755020745346], [1740877332521282793, "rand_core", false, 2151513396798926002], [4684437522915235464, "libc", false, 10902910220991786947], [5170503507811329045, "getrandom_package", false, 549635512286065446]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rand-085ef9c960fa299a/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}