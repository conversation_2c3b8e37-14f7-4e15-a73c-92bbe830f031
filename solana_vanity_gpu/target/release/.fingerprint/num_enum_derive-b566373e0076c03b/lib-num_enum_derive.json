{"rustc": 4723136837156968084, "features": "[\"proc-macro-crate\", \"std\"]", "declared_features": "[\"complex-expressions\", \"default\", \"external_doc\", \"proc-macro-crate\", \"std\"]", "target": 15019087522015688764, "profile": 17257705230225558938, "path": 11023564309398932775, "deps": [[3060637413840920116, "proc_macro2", false, 8654979111247074264], [4974441333307933176, "syn", false, 5533940753606354596], [15203748914246919255, "proc_macro_crate", false, 15210026800580999932], [17990358020177143287, "quote", false, 3345825101821211705]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/num_enum_derive-b566373e0076c03b/dep-lib-num_enum_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}