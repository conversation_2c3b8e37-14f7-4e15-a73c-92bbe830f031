{"rustc": 4723136837156968084, "features": "[]", "declared_features": "[\"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 6875603382767429092, "profile": 2554159132106277380, "path": 1876978258962255476, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 17836248047607515518], [17990358020177143287, "quote", false, 3345825101821211705]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-macro-8d28463bea1f2ede/dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}