cargo:rustc-check-cfg=cfg(blake3_sse2_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse2_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse41_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_sse41_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx2_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx2_rust, values(none()))
cargo:rustc-check-cfg=cfg(blake3_avx512_ffi, values(none()))
cargo:rustc-check-cfg=cfg(blake3_neon, values(none()))
cargo:rustc-check-cfg=cfg(blake3_wasm32_simd, values(none()))
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rerun-if-env-changed=CARGO_FEATURE_NO_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_NO_NEON
cargo:rerun-if-env-changed=CARGO_FEATURE_PURE
cargo:rustc-cfg=blake3_neon
OUT_DIR = Some(/Users/<USER>/Documents/Projects/sol_wallet_gen/solana_vanity_gpu/target/release/build/blake3-56ed82aef2f23a9a/out)
OPT_LEVEL = Some(3)
TARGET = Some(aarch64-apple-darwin)
HOST = Some(aarch64-apple-darwin)
CC_aarch64-apple-darwin = None
CC_aarch64_apple_darwin = None
HOST_CC = None
CC = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
RUSTC_WRAPPER = None
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(false)
MACOSX_DEPLOYMENT_TARGET = None
CFLAGS = None
HOST_CFLAGS = None
CFLAGS_aarch64_apple_darwin = None
CFLAGS_aarch64-apple-darwin = None
CARGO_ENCODED_RUSTFLAGS = Some()
AR_aarch64-apple-darwin = None
AR_aarch64_apple_darwin = None
HOST_AR = None
AR = None
ARFLAGS = None
HOST_ARFLAGS = None
ARFLAGS_aarch64_apple_darwin = None
ARFLAGS_aarch64-apple-darwin = None
cargo:rustc-link-lib=static=blake3_neon
cargo:rustc-link-search=native=/Users/<USER>/Documents/Projects/sol_wallet_gen/solana_vanity_gpu/target/release/build/blake3-56ed82aef2f23a9a/out
cargo:rerun-if-env-changed=CC
cargo:rerun-if-env-changed=CFLAGS
cargo:rerun-if-changed=c/blake3_sse41_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c/blake3_avx512_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c/blake3_sse2_x86-64_unix.S
cargo:rerun-if-changed=c/blake3_sse2_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c/CMakeLists.txt
cargo:rerun-if-changed=c/libblake3.pc.in
cargo:rerun-if-changed=c/cmake
cargo:rerun-if-changed=c/blake3_sse41_x86-64_unix.S
cargo:rerun-if-changed=c/blake3-config.cmake.in
cargo:rerun-if-changed=c/blake3.h
cargo:rerun-if-changed=c/blake3_dispatch.c
cargo:rerun-if-changed=c/blake3_sse41.c
cargo:rerun-if-changed=c/blake3_avx512_x86-64_windows_gnu.S
cargo:rerun-if-changed=c/dependencies
cargo:rerun-if-changed=c/Makefile.testing
cargo:rerun-if-changed=c/test.py
cargo:rerun-if-changed=c/blake3_portable.c
cargo:rerun-if-changed=c/blake3_tbb.cpp
cargo:rerun-if-changed=c/blake3_neon.c
cargo:rerun-if-changed=c/blake3_avx512.c
cargo:rerun-if-changed=c/README.md
cargo:rerun-if-changed=c/CMakePresets.json
cargo:rerun-if-changed=c/example.c
cargo:rerun-if-changed=c/blake3_avx2.c
cargo:rerun-if-changed=c/main.c
cargo:rerun-if-changed=c/.gitignore
cargo:rerun-if-changed=c/blake3_avx2_x86-64_unix.S
cargo:rerun-if-changed=c/blake3_avx2_x86-64_windows_gnu.S
cargo:rerun-if-changed=c/blake3.c
cargo:rerun-if-changed=c/example_tbb.c
cargo:rerun-if-changed=c/blake3_sse2_x86-64_windows_gnu.S
cargo:rerun-if-changed=c/blake3_impl.h
cargo:rerun-if-changed=c/blake3_sse41_x86-64_windows_gnu.S
cargo:rerun-if-changed=c/blake3_avx2_x86-64_windows_msvc.asm
cargo:rerun-if-changed=c/blake3_sse2.c
cargo:rerun-if-changed=c/blake3_avx512_x86-64_unix.S
