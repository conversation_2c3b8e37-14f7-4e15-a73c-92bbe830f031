# Solana Vanity Address Generator

Fast Rust-based vanity address generator for Solana. Generates addresses that start or end with your desired patterns.

## Performance
Tested on Macbook Pro M1 Pro 16" (8 CPU cores):
- Average speed: **~500K+ addresses/second**
- Uses all CPU cores automatically

## Installation

### Step 1: Install Rust
```bash
# On Mac/Linux:
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# On Windows: Download and run from https://rustup.rs/
```

### Step 2: Clone/Download Code
```bash
# If you have the code folder, navigate to it:
cd rust_v

# Or download from your source
```

### Step 3: Build
```bash
cargo build --release
```

## Usage

### Basic Usage
```bash
# Search for addresses starting or ending with "ARTIN" (default)
./target/release/solana_vanity_generator ARTIN

# Search for multiple patterns
./target/release/solana_vanity_generator ARTIN Artin ART1N

# Search for different patterns
./target/release/solana_vanity_generator Sol DOGE MOON
```

### Position Control
```bash
# Only at the START of address
./target/release/solana_vanity_generator --position start ARTIN

# Only at the END of address
./target/release/solana_vanity_generator --position end ARTIN

# At START OR END (default behavior)
./target/release/solana_vanity_generator --position startorend ARTIN

# ANYWHERE in the address
./target/release/solana_vanity_generator --position anywhere ARTIN

# Combine position with multiple patterns
./target/release/solana_vanity_generator --position start ARTIN Sol DOGE
```

### Example Output
```
Solana Vanity Address Generator
Using command line patterns: ARTIN
Match position: StartOrEnd
Case sensitive: true
Using 8 CPU threads with batch size 100000

Attempts: 2.50M | Current Rate: 526.62K addr/sec | Avg Rate: 526.62K addr/sec | Running: 5s

Found matching address!
Matched pattern: ARTIN
Match position: start
Actual match: ARTIN
Public Key: ARTINxxx...xxxxx
Secret Key: 5Kxxx...xxxxx
Total attempts: 3,247,891
Time taken: 6m 17s
Speed: 526,234 addresses/second
```

## Important Notes

### Valid Characters
Only use **Base58** characters in patterns:
```
**********************************************************
```

**Invalid characters:** `0` (zero), `O` (capital O), `I` (capital i), `l` (lowercase L)

### Security
- **Save your private key immediately** - it's only shown once
- **Test with small amounts first** before using for real funds
- Generated keys are cryptographically secure

### Performance Tips
- **Shorter patterns** = faster generation
- **Longer patterns** = exponentially slower
- **4-5 characters** = reasonable time
- **6+ characters** = may take hours/days
- **Position matters**: `start` or `end` is faster than `anywhere`
- **Multiple patterns**: Searches for ANY match, not all patterns

## Troubleshooting

### Build Errors
```bash
# Update Rust
rustup update

# Clean and rebuild
cargo clean
cargo build --release
```

### Pattern Not Found
- Try shorter patterns
- Check for invalid Base58 characters
- Be patient - longer patterns take time

### Performance Issues
- Close other applications
- Ensure you're using `--release` build
- Check CPU temperature (throttling)

## Customization

Edit `src/main.rs` to change default patterns:
```rust
// Change this line at the top of the file
const POSSIBLE_PATTERNS: &[&str] = &["YourPattern"];
```

Then rebuild: `cargo build --release`

## Platform Support
- ✅ **macOS** (Intel & M1)
- ✅ **Windows** (Intel & AMD)
- ✅ **Linux** (Intel & AMD)

## License
Use at your own risk. No warranty provided.
