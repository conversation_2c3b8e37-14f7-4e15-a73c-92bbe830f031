use solana_sdk::signature::{Keypair, Signer};
use solana_sdk::bs58;
use std::time::{Instant, Duration};
use std::sync::{Arc, atomic::{AtomicU64, AtomicBool, Ordering}};
use std::thread;
use std::io::{self, Write};
use rayon::prelude::*;

// Helper function to format large numbers
fn format_number(num: f64) -> String {
    if num >= 1_000_000_000.0 {
        format!("{:.2}B", num / 1_000_000_000.0)
    } else if num >= 1_000_000.0 {
        format!("{:.2}M", num / 1_000_000.0)
    } else if num >= 1_000.0 {
        format!("{:.2}K", num / 1_000.0)
    } else {
        format!("{:.0}", num)
    }
}

// Configuration constants matching Node.js version
const CASE_SENSITIVE: bool = true;
const BATCH_SIZE: usize = 100_000; // Optimized batch size for Rust

// Possible patterns to search for
const POSSIBLE_PATTERNS: &[&str] = &["Arti"];

#[derive(Debug, Clone)]
enum MatchPosition {
    StartOnly,
    EndOnly,
    StartOrEnd,
    Anywhere,
}

#[derive(Debug, Clone)]
struct PatternData {
    pattern: String,
    length: usize,
    lower_pattern: String,
    compare_pattern: String,
}

impl PatternData {
    fn new(pattern: &str) -> Self {
        Self {
            pattern: pattern.to_string(),
            length: pattern.len(),
            lower_pattern: pattern.to_lowercase(),
            compare_pattern: if CASE_SENSITIVE { pattern.to_string() } else { pattern.to_lowercase() },
        }
    }
}

fn validate_prefix(prefix: &str) -> bool {
    let base58_alphabet = "123456789ABCDEFGHIJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";
    prefix.chars().all(|c| base58_alphabet.contains(c))
}

fn check_address(address: &str, pattern: &PatternData, match_position: &MatchPosition) -> Option<(String, String)> {
    let compare_address = if CASE_SENSITIVE { address } else { &address.to_lowercase() };
    let compare_pattern = &pattern.compare_pattern;

    match match_position {
        MatchPosition::StartOnly => {
            if compare_address.starts_with(compare_pattern) {
                Some(("start".to_string(), address[..pattern.length].to_string()))
            } else {
                None
            }
        }
        MatchPosition::EndOnly => {
            if compare_address.ends_with(compare_pattern) {
                Some(("end".to_string(), address[address.len() - pattern.length..].to_string()))
            } else {
                None
            }
        }
        MatchPosition::StartOrEnd => {
            if compare_address.starts_with(compare_pattern) {
                Some(("start".to_string(), address[..pattern.length].to_string()))
            } else if compare_address.ends_with(compare_pattern) {
                Some(("end".to_string(), address[address.len() - pattern.length..].to_string()))
            } else {
                None
            }
        }
        MatchPosition::Anywhere => {
            if let Some(index) = compare_address.find(compare_pattern) {
                Some((format!("index {}", index), address[index..index + pattern.length].to_string()))
            } else {
                None
            }
        }
    }
}

fn main() {
    // Check for command line arguments first, otherwise use default patterns
    let args: Vec<String> = std::env::args().collect();
    let possible_patterns: Vec<String> = if args.len() > 1 {
        // Use command line arguments as patterns
        args[1..].to_vec()
    } else {
        // Use default patterns from constant
        POSSIBLE_PATTERNS.iter().map(|s| s.to_string()).collect()
    };
    let match_position = MatchPosition::StartOrEnd; // Can be changed as needed

    let start_time = Instant::now();
    let attempts = Arc::new(AtomicU64::new(0));
    let found = Arc::new(AtomicBool::new(false));
    let found_keypair = Arc::new(parking_lot::Mutex::new(None::<(String, String, String, String, String)>));

    // Validate all patterns
    for pattern in &possible_patterns {
        if !validate_prefix(pattern) {
            eprintln!("Error: Pattern \"{}\" contains invalid Base58 characters", pattern);
            eprintln!("Valid characters are: 123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz");
            std::process::exit(1);
        }
    }

    // Pre-compute pattern data
    let pattern_data: Vec<PatternData> = possible_patterns.iter()
        .map(|p| PatternData::new(p))
        .collect();

    println!("Solana Vanity Address Generator");
    if args.len() > 1 {
        println!("Using command line patterns: {}", possible_patterns.join(", "));
    } else {
        println!("Using default patterns: {} (use: ./target/release/solana_vanity_generator PATTERN1 PATTERN2 ...)", possible_patterns.join(", "));
    }
    println!("Match position: {:?}", match_position);
    println!("Case sensitive: {}", CASE_SENSITIVE);
    println!("Using {} CPU threads with batch size {}", num_cpus::get(), BATCH_SIZE);

    // Progress reporting thread
    let attempts_clone = Arc::clone(&attempts);
    let found_clone = Arc::clone(&found);
    let start_time_clone = start_time;
    thread::spawn(move || {
        let mut last_report_time = start_time_clone;
        let mut last_report_attempts = 0u64;

        while !found_clone.load(Ordering::Relaxed) {
            thread::sleep(Duration::from_secs(1));
            let now = Instant::now();
            let elapsed = now.duration_since(start_time_clone).as_secs_f64();
            let total_attempts = attempts_clone.load(Ordering::Relaxed);

            let time_span = now.duration_since(last_report_time).as_secs_f64();
            let recent_attempts = total_attempts - last_report_attempts;
            let current_rate = if time_span > 0.0 { recent_attempts as f64 / time_span } else { 0.0 };
            let avg_rate = if elapsed > 0.0 { total_attempts as f64 / elapsed } else { 0.0 };

            print!("\rAttempts: {} | Current Rate: {} addr/sec | Avg Rate: {} addr/sec | Running: {}s",
                format_number(total_attempts as f64),
                format_number(current_rate),
                format_number(avg_rate),
                elapsed as u64
            );
            io::stdout().flush().unwrap();

            last_report_time = now;
            last_report_attempts = total_attempts;
        }
    });

    // Parallel keypair generation with batching
    (0..num_cpus::get()).into_par_iter().for_each(|_worker_id| {
        let mut local_attempts = 0u64;
        let report_threshold = 10_000u64;

        while !found.load(Ordering::Relaxed) {
            // Generate keypairs in batches for better performance
            for _ in 0..BATCH_SIZE {
                if found.load(Ordering::Relaxed) {
                    break;
                }

                let keypair = Keypair::new();
                let pubkey = keypair.pubkey().to_string();
                local_attempts += 1;

                // Check against all patterns
                for pattern in &pattern_data {
                    if let Some((position, actual_match)) = check_address(&pubkey, pattern, &match_position) {
                        found.store(true, Ordering::Relaxed);
                        let private_key = bs58::encode(keypair.to_bytes()).into_string();
                        *found_keypair.lock() = Some((
                            pattern.pattern.clone(),
                            position,
                            actual_match,
                            pubkey,
                            private_key
                        ));
                        attempts.fetch_add(local_attempts, Ordering::Relaxed);
                        return;
                    }
                }

                // Report progress periodically
                if local_attempts % report_threshold == 0 {
                    attempts.fetch_add(report_threshold, Ordering::Relaxed);
                    local_attempts = 0;
                }
            }

            // Report remaining attempts
            if local_attempts > 0 {
                attempts.fetch_add(local_attempts, Ordering::Relaxed);
                local_attempts = 0;
            }
        }
    });

    let result = found_keypair.lock().clone();
    if let Some((matched_pattern, position, actual_match, pubkey, private_key)) = result {
        let elapsed_time = start_time.elapsed().as_secs_f64();
        let total_attempts = attempts.load(Ordering::Relaxed);

        println!("\n\nFound matching address!");
        println!("Matched pattern: {}", matched_pattern);
        println!("Match position: {}", position);
        println!("Actual match: {}", actual_match);
        println!("Public Key: {}", pubkey);
        println!("Secret Key: {}", private_key);
        println!("Total attempts: {}", total_attempts);
        println!("Time taken: {:.2} seconds", elapsed_time);
        println!("Speed: {} addresses/second", format_number(total_attempts as f64 / elapsed_time));
    }
}
