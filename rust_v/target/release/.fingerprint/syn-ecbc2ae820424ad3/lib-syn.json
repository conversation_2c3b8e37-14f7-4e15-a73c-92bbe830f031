{"rustc": 4723136837156968084, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 17257705230225558938, "path": 12309718783314365728, "deps": [[1988483478007900009, "unicode_ident", false, 16637497482365649301], [12410540580958238005, "proc_macro2", false, 1253931232196190915], [17990358020177143287, "quote", false, 14245013045785092098]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/syn-ecbc2ae820424ad3/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}