{"rustc": 4723136837156968084, "features": "[\"default\"]", "declared_features": "[\"asm\", \"default\", \"parallel\", \"rayon\", \"std\"]", "target": 4360302069253712615, "profile": 6418093726722172150, "path": 5944131557564890203, "deps": [[477150410136574819, "ark_ff_macros", false, 1344131191445101026], [2932480923465029663, "zeroize", false, 2511760589653641772], [5157631553186200874, "num_traits", false, 8390599088884525261], [11903278875415370753, "itertools", false, 18314660748751123816], [12528732512569713347, "num_bigint", false, 15353381454479953559], [13859769749131231458, "derivative", false, 12600527674659395021], [15179503056858879355, "ark_std", false, 1366060526214656231], [16925068697324277505, "ark_serialize", false, 10261714147134223540], [17475753849556516473, "digest", false, 9371934103501544488], [17605717126308396068, "paste", false, 9140080941227406887], [17996237327373919127, "ark_ff_asm", false, 14516567847717232022]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/ark-ff-31073b191bfa9610/dep-lib-ark_ff", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}