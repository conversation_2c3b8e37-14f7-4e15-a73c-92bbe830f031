{"rustc": 4723136837156968084, "features": "[\"assert_matches\", \"byteorder\", \"chrono\", \"default\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\", \"solana-logger\"]", "declared_features": "[\"assert_matches\", \"byteorder\", \"chrono\", \"curve25519-dalek\", \"default\", \"dev-context-only-utils\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"program\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\", \"solana-logger\"]", "target": 5408242616063297496, "profile": 17257705230225558938, "path": 15418210222767071616, "deps": [[8576480473721236041, "rustc_version", false, 17433704572165577934]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/solana-sdk-9e4d6aa85db02b56/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}