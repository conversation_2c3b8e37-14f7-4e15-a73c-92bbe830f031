{"rustc": 4723136837156968084, "features": "[\"bytemuck_derive\", \"derive\"]", "declared_features": "[\"aarch64_simd\", \"align_offset\", \"alloc_uninit\", \"avx512_simd\", \"bytemuck_derive\", \"const_zeroed\", \"derive\", \"extern_crate_alloc\", \"extern_crate_std\", \"latest_stable_rust\", \"min_const_generics\", \"must_cast\", \"must_cast_extra\", \"nightly_docs\", \"nightly_float\", \"nightly_portable_simd\", \"nightly_stdsimd\", \"pod_saturating\", \"track_caller\", \"transparentwrapper_extra\", \"unsound_ptr_pod_impl\", \"wasm_simd\", \"zeroable_atomics\", \"zeroable_maybe_uninit\"]", "target": 5195934831136530909, "profile": 13927646211655395056, "path": 670935379378749304, "deps": [[7596180949490085099, "bytemuck_derive", false, 16672143056323825191]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/bytemuck-003dc51de048d38c/dep-lib-bytemuck", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}