{"rustc": 4723136837156968084, "features": "[\"assert_matches\", \"byteorder\", \"chrono\", \"default\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\", \"solana-logger\"]", "declared_features": "[\"assert_matches\", \"byteorder\", \"chrono\", \"curve25519-dalek\", \"default\", \"dev-context-only-utils\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"program\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\", \"solana-logger\"]", "target": 12656250652134757477, "profile": 6418093726722172150, "path": 11500654221973114674, "deps": [[58942224022519477, "solana_program", false, 10088332881166568737], [65234016722529558, "bincode", false, 10151937220233903602], [424821569244661989, "solana_frozen_abi", false, 9841316614797921628], [757899038044743028, "serde_with", false, 11196189552056231106], [1230783206204459120, "urip<PERSON>e", false, 11067724837219581638], [1470679118034951355, "num_enum", false, 13197912241262445349], [1999399154011168049, "rustversion", false, 1666112542634812873], [2846752081260175975, "log", false, 8488919472366235570], [3070183601088350567, "bytemuck", false, 5438093408979774560], [3712811570531045576, "byteorder", false, 7729625386954667301], [4097734106057062256, "bs58", false, 13687185604312180044], [4258399515347749257, "pbkdf2", false, 1729691593152632150], [4413975316754495123, "build_script_build", false, 18094083646244663991], [4731167174326621189, "rand0_7", false, 8585139833809311203], [5092398082731730447, "derivation_path", false, 12268420215905364894], [5157631553186200874, "num_traits", false, 8390599088884525261], [5236433071915784494, "sha2", false, 10972773546565908215], [5892263340167577622, "solana_logger", false, 12420506307000131764], [6166349630582887940, "bitflags", false, 16799037094015231427], [6547980334806251551, "chrono", false, 13286580361850929269], [6946689283190175495, "wasm_bindgen", false, 13231224765425331726], [8008191657135824715, "thiserror", false, 11853766768043018911], [8057773192621180530, "borsh", false, 17797013174001434090], [8079500665534101559, "siphasher", false, 10098274046184050381], [9077477275112005502, "solana_sdk_macro", false, 16521986489277656159], [9209347893430674936, "hmac", false, 12109070882354539418], [9689903380558560274, "serde", false, 9941170934302375922], [10504454274054532777, "memmap2", false, 1103597116754059946], [10520923840501062997, "generic_array", false, 1369877014019221756], [10697153736615144157, "libsecp256k1", false, 17460574848191526250], [10889494155287625682, "serde_bytes", false, 1826541279560921311], [11017232866922121725, "sha3", false, 11736550289696157195], [11263754829263059703, "num_derive", false, 4873865179313230331], [11903278875415370753, "itertools", false, 18314660748751123816], [13024038960712206194, "qstring", false, 18177595640122549379], [13208667028893622512, "rand", false, 2087407714700763708], [14165672584825129001, "solana_frozen_abi_macro", false, 11689971989927192341], [15367738274754116744, "serde_json", false, 12219963101604047670], [15407337108592562583, "ed25519_dalek_bip32", false, 10583194875503318868], [15877171306855209770, "qualifier_attr", false, 614002667233884943], [16257276029081467297, "serde_derive", false, 8071280183083621218], [16912878040847476921, "assert_matches", false, 11001776118897151093], [17475753849556516473, "digest", false, 9371934103501544488], [17917672826516349275, "lazy_static", false, 2139054082405725281], [17987314850127689447, "ed25519_dalek", false, 12528844763292270090], [18066890886671768183, "base64", false, 801767773081047609]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/solana-sdk-4ba48d9c17358f5d/dep-lib-solana_sdk", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}