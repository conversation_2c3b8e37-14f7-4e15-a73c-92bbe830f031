{"rustc": 4723136837156968084, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 4946792044405695984, "path": 11225361072221459679, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/winnow-d61feeb577788767/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}