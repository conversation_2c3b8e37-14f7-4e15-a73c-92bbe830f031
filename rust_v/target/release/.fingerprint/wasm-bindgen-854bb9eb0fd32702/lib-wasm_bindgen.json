{"rustc": 4723136837156968084, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 11373124797044503663, "path": 4069930181883537966, "deps": [[1213962973451362254, "once_cell", false, 9373951237482717558], [1999399154011168049, "rustversion", false, 1666112542634812873], [6946689283190175495, "build_script_build", false, 11173532801067730808], [10411997081178400487, "cfg_if", false, 13020194419435049427], [11382113702854245495, "wasm_bindgen_macro", false, 9549113874851776319]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wasm-bindgen-854bb9eb0fd32702/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}