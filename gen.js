const { Keypair } = require('@solana/web3.js');
const bs58 = require('bs58').default;
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const os = require('os');
const crypto = require('crypto');

// Configuration constants
const CASE_SENSITIVE = true; // Set to true for case-sensitive matching
const MATCH_POSITION = 'START_ONLY'; // Options: 'START_ONLY', 'END_ONLY', 'START_OR_END', 'ANYWHERE'
const numCPUs = os.cpus().length;
const BATCH_SIZE = 500000; // Increased batch size
const REPORT_THRESHOLD = 100000; // Report less frequently to reduce IPC overhead

// Correct Base58 alphabet (excludes 0, O, I, l to avoid confusion)
const base58Chars = '**********************************************************';
const isValidBase58 = (str) => [...str].every(char => base58Chars.includes(char));

// Define possible patterns we want to match (using valid Base58 characters)
const possiblePatterns = [
    'Art1n',  // Changed I to 1 (valid Base58)
    'ART1N'   // Changed I to 1 (valid Base58)
];

// Pre-compute pattern data
const patternData = possiblePatterns.map(pattern => ({
    pattern,
    length: pattern.length,
    lowerPattern: pattern.toLowerCase(),
    comparePattern: CASE_SENSITIVE ? pattern : pattern.toLowerCase()
}));

// Optimized key generation using pre-allocated buffers and memory pool
class OptimizedKeyGenerator {
    constructor() {
        this.poolSize = 1000;
        this.currentPoolIndex = 0;
        this.secretKeyPool = new Array(this.poolSize);
        for (let i = 0; i < this.poolSize; i++) {
            this.secretKeyPool[i] = new Uint8Array(32);
        }
        this.batchBuffer = new Uint8Array(32 * BATCH_SIZE);
    }

    getNextBuffer() {
        const buffer = this.secretKeyPool[this.currentPoolIndex];
        this.currentPoolIndex = (this.currentPoolIndex + 1) % this.poolSize;
        return buffer;
    }

    generateBatchRandomBytes() {
        crypto.randomFillSync(this.batchBuffer);
        return this.batchBuffer;
    }
}

if (isMainThread) {
    // Validate all patterns
    patternData.forEach(({ pattern }) => {
        if (!isValidBase58(pattern)) {
            console.error(`Error: Pattern "${pattern}" contains invalid Base58 characters`);
            console.error('Valid characters are:', base58Chars);
            process.exit(1);
        }
    });

    console.log('Searching for Solana address starting OR ending with any of these patterns (case-insensitive):');
    console.log(possiblePatterns.join(', '));
    console.log(`Using ${numCPUs} worker threads with optimized batching`);

    let startTime = Date.now();
    let totalAttempts = 0;
    let running = true;
    let lastReportTime = Date.now();
    let lastReportAttempts = 0;

    const workers = [];

    for (let i = 0; i < numCPUs; i++) {
        const worker = new Worker(__filename, {
            workerData: { patterns: patternData, workerId: i }
        });

        workers.push(worker);

        worker.on('message', (result) => {
            if (result.found) {
                console.log('\nFound matching address!');
                console.log(`Matched pattern: ${result.matchedPattern}`);
                console.log(`Match position: ${result.position}`);
                console.log(`Actual match: ${result.actualMatch}`);
                console.log(`Public Key: ${result.publicKey}`);
                console.log(`Secret Key: ${result.secretKey}`);
                console.log(`Found by Worker #${result.workerId}`);
                console.log(`Total attempts: ${totalAttempts + result.attempts}`);

                const duration = (Date.now() - startTime) / 1000;
                console.log(`Time taken: ${duration.toFixed(2)} seconds`);
                console.log(`Speed: ${Math.floor((totalAttempts + result.attempts) / duration)} addresses/second`);

                running = false;
                workers.forEach(w => w.terminate());
                process.exit(0);
            } else {
                totalAttempts += result.attempts;
                const now = Date.now();
                if (running && now - lastReportTime >= 1000) {
                    const timeSpan = (now - lastReportTime) / 1000;
                    const recentAttempts = totalAttempts - lastReportAttempts;
                    const currentRate = Math.floor(recentAttempts / timeSpan);
                    const totalRate = Math.floor(totalAttempts / ((now - startTime) / 1000));

                    process.stdout.write(`\rAttempts: ${totalAttempts.toLocaleString()} | Current Rate: ${currentRate.toLocaleString()} addr/sec | Avg Rate: ${totalRate.toLocaleString()} addr/sec | Running: ${((now - startTime) / 1000).toFixed(0)}s`);

                    lastReportTime = now;
                    lastReportAttempts = totalAttempts;
                }
            }
        });

        worker.on('error', (err) => {
            console.error(`Worker #${i} error:`, err);
        });
    }

} else {
    const { patterns, workerId } = workerData;
    let attempts = 0;
    let lastReportAttempts = 0;

    const keyGen = new OptimizedKeyGenerator();

    function checkAddress(address, pattern) {
        const compareAddress = CASE_SENSITIVE ? address : address.toLowerCase();
        const comparePattern = CASE_SENSITIVE ? pattern.pattern : pattern.lowerPattern;

        switch (MATCH_POSITION) {
            case 'START_ONLY':
                if (compareAddress.startsWith(comparePattern)) {
                    return {
                        found: true,
                        position: 'start',
                        actualMatch: address.slice(0, pattern.length)
                    };
                }
                break;

            case 'END_ONLY':
                if (compareAddress.endsWith(comparePattern)) {
                    return {
                        found: true,
                        position: 'end',
                        actualMatch: address.slice(-pattern.length)
                    };
                }
                break;

            case 'START_OR_END':
                if (compareAddress.startsWith(comparePattern)) {
                    return {
                        found: true,
                        position: 'start',
                        actualMatch: address.slice(0, pattern.length)
                    };
                }
                if (compareAddress.endsWith(comparePattern)) {
                    return {
                        found: true,
                        position: 'end',
                        actualMatch: address.slice(-pattern.length)
                    };
                }
                break;

            case 'ANYWHERE':
                const index = compareAddress.indexOf(comparePattern);
                if (index !== -1) {
                    return {
                        found: true,
                        position: `index ${index}`,
                        actualMatch: address.slice(index, index + pattern.length)
                    };
                }
                break;
        }

        return { found: false };
    }

    function searchForVanityAddress() {
        while (true) {
            const batchBuffer = keyGen.generateBatchRandomBytes();

            for (let i = 0; i < BATCH_SIZE; i++) {
                attempts++;

                const secretKeyBuffer = keyGen.getNextBuffer();
                secretKeyBuffer.set(batchBuffer.slice(i * 32, (i + 1) * 32));

                const keypair = Keypair.fromSeed(secretKeyBuffer);
                const publicKey = keypair.publicKey.toBase58();

                // Check against all possible patterns
                for (const pattern of patterns) {
                    const result = checkAddress(publicKey, pattern);
                    if (result.found) {
                        parentPort.postMessage({
                            found: true,
                            workerId,
                            matchedPattern: pattern.pattern,
                            position: result.position,
                            actualMatch: result.actualMatch,
                            publicKey,
                            secretKey: bs58.encode(keypair.secretKey),
                            attempts
                        });
                        return;
                    }
                }

                // Report progress less frequently to reduce IPC overhead
                if (attempts - lastReportAttempts >= REPORT_THRESHOLD) {
                    parentPort.postMessage({
                        found: false,
                        workerId,
                        attempts: attempts - lastReportAttempts
                    });
                    lastReportAttempts = attempts;
                }
            }
        }
    }

    searchForVanityAddress();
}
