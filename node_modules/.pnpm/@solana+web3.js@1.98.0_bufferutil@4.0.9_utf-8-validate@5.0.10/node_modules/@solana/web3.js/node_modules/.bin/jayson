#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/jayson@4.1.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/jayson/bin/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/jayson@4.1.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/jayson/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/jayson@4.1.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/jayson@4.1.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/jayson/bin/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/jayson@4.1.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/jayson/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/jayson@4.1.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../jayson@4.1.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/jayson/bin/jayson.js" "$@"
else
  exec node  "$basedir/../../../../../../jayson@4.1.3_bufferutil@4.0.9_utf-8-validate@5.0.10/node_modules/jayson/bin/jayson.js" "$@"
fi
