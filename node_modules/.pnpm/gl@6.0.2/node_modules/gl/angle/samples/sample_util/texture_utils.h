//
// Copyright (c) 2014 The ANGLE Project Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
//

#ifndef SAMPLE_UTIL_TEXTURE_UTILS_H
#define SAMPLE_UTIL_TEXTURE_UTILS_H

#include <GLES2/gl2.h>

GLuint CreateSimpleTexture2D();
GLuint CreateSimpleTextureCubemap();

GLuint CreateMipMappedTexture2D();

#endif // SAMPLE_UTIL_TEXTURE_UTILS_H
