/* A Bison parser, made by GNU Bison 3.0.4.  */

/* Bison implementation for Yacc-like parsers in C

   Copyright (C) 1984, 1989-1990, 2000-2015 Free Software Foundation, Inc.

   This program is free software: you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation, either version 3 of the License, or
   (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program.  If not, see <http://www.gnu.org/licenses/>.  */

/* As a special exception, you may create a larger work that contains
   part or all of the Bison parser skeleton and distribute that work
   under terms of your choice, so long as that work isn't itself a
   parser generator using the skeleton or a modified version thereof
   as a parser skeleton.  Alternatively, if you modify or redistribute
   the parser skeleton itself, you may (at your option) remove this
   special exception, which will cause the skeleton and the resulting
   Bison output files to be licensed under the GNU General Public
   License without this special exception.

   This special exception was added by the Free Software Foundation in
   version 2.2 of Bison.  */

/* C LALR(1) parser skeleton written by <PERSON>, by
   simplifying the original so-called "semantic" parser.  */

/* All symbols defined below should begin with yy or YY, to avoid
   infringing on user name space.  This should be done even for local
   variables, as they might otherwise be expanded by user macros.
   There are some unavoidable exceptions within include files to
   define necessary library symbols; they are noted "INFRINGES ON
   USER NAME SPACE" below.  */

/* Identify Bison output.  */
#define YYBISON 1

/* Bison version.  */
#define YYBISON_VERSION "3.0.4"

/* Skeleton name.  */
#define YYSKELETON_NAME "yacc.c"

/* Pure parsers.  */
#define YYPURE 2

/* Push parsers.  */
#define YYPUSH 0

/* Pull parsers.  */
#define YYPULL 1




/* Copy the first part of user declarations.  */


//
// Copyright (c) 2002-2014 The ANGLE Project Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
//

// This file is auto-generated by generate_parser.sh. DO NOT EDIT!

// Ignore errors in auto-generated code.
#if defined(__GNUC__)
#pragma GCC diagnostic ignored "-Wunused-function"
#pragma GCC diagnostic ignored "-Wunused-variable"
#pragma GCC diagnostic ignored "-Wswitch-enum"
#elif defined(_MSC_VER)
#pragma warning(disable: 4065)
#pragma warning(disable: 4189)
#pragma warning(disable: 4244)
#pragma warning(disable: 4505)
#pragma warning(disable: 4701)
#pragma warning(disable: 4702)
#endif

#include "angle_gl.h"
#include "compiler/translator/Cache.h"
#include "compiler/translator/SymbolTable.h"
#include "compiler/translator/ParseContext.h"
#include "GLSLANG/ShaderLang.h"

#define YYENABLE_NLS 0




# ifndef YY_NULLPTR
#  if defined __cplusplus && 201103L <= __cplusplus
#   define YY_NULLPTR nullptr
#  else
#   define YY_NULLPTR 0
#  endif
# endif

/* Enabling verbose error messages.  */
#ifdef YYERROR_VERBOSE
# undef YYERROR_VERBOSE
# define YYERROR_VERBOSE 1
#else
# define YYERROR_VERBOSE 0
#endif

/* In a future release of Bison, this section will be replaced
   by #include "glslang_tab.h".  */
#ifndef YY_YY_GLSLANG_TAB_H_INCLUDED
# define YY_YY_GLSLANG_TAB_H_INCLUDED
/* Debug traces.  */
#ifndef YYDEBUG
# define YYDEBUG 0
#endif
#if YYDEBUG
extern int yydebug;
#endif
/* "%code requires" blocks.  */


#define YYLTYPE TSourceLoc
#define YYLTYPE_IS_DECLARED 1



/* Token type.  */
#ifndef YYTOKENTYPE
# define YYTOKENTYPE
  enum yytokentype
  {
    INVARIANT = 258,
    HIGH_PRECISION = 259,
    MEDIUM_PRECISION = 260,
    LOW_PRECISION = 261,
    PRECISION = 262,
    ATTRIBUTE = 263,
    CONST_QUAL = 264,
    BOOL_TYPE = 265,
    FLOAT_TYPE = 266,
    INT_TYPE = 267,
    UINT_TYPE = 268,
    BREAK = 269,
    CONTINUE = 270,
    DO = 271,
    ELSE = 272,
    FOR = 273,
    IF = 274,
    DISCARD = 275,
    RETURN = 276,
    SWITCH = 277,
    CASE = 278,
    DEFAULT = 279,
    BVEC2 = 280,
    BVEC3 = 281,
    BVEC4 = 282,
    IVEC2 = 283,
    IVEC3 = 284,
    IVEC4 = 285,
    VEC2 = 286,
    VEC3 = 287,
    VEC4 = 288,
    UVEC2 = 289,
    UVEC3 = 290,
    UVEC4 = 291,
    MATRIX2 = 292,
    MATRIX3 = 293,
    MATRIX4 = 294,
    IN_QUAL = 295,
    OUT_QUAL = 296,
    INOUT_QUAL = 297,
    UNIFORM = 298,
    VARYING = 299,
    MATRIX2x3 = 300,
    MATRIX3x2 = 301,
    MATRIX2x4 = 302,
    MATRIX4x2 = 303,
    MATRIX3x4 = 304,
    MATRIX4x3 = 305,
    CENTROID = 306,
    FLAT = 307,
    SMOOTH = 308,
    STRUCT = 309,
    VOID_TYPE = 310,
    WHILE = 311,
    SAMPLER2D = 312,
    SAMPLERCUBE = 313,
    SAMPLER_EXTERNAL_OES = 314,
    SAMPLER2DRECT = 315,
    SAMPLER2DARRAY = 316,
    ISAMPLER2D = 317,
    ISAMPLER3D = 318,
    ISAMPLERCUBE = 319,
    ISAMPLER2DARRAY = 320,
    USAMPLER2D = 321,
    USAMPLER3D = 322,
    USAMPLERCUBE = 323,
    USAMPLER2DARRAY = 324,
    SAMPLER3D = 325,
    SAMPLER3DRECT = 326,
    SAMPLER2DSHADOW = 327,
    SAMPLERCUBESHADOW = 328,
    SAMPLER2DARRAYSHADOW = 329,
    LAYOUT = 330,
    IDENTIFIER = 331,
    TYPE_NAME = 332,
    FLOATCONSTANT = 333,
    INTCONSTANT = 334,
    UINTCONSTANT = 335,
    BOOLCONSTANT = 336,
    FIELD_SELECTION = 337,
    LEFT_OP = 338,
    RIGHT_OP = 339,
    INC_OP = 340,
    DEC_OP = 341,
    LE_OP = 342,
    GE_OP = 343,
    EQ_OP = 344,
    NE_OP = 345,
    AND_OP = 346,
    OR_OP = 347,
    XOR_OP = 348,
    MUL_ASSIGN = 349,
    DIV_ASSIGN = 350,
    ADD_ASSIGN = 351,
    MOD_ASSIGN = 352,
    LEFT_ASSIGN = 353,
    RIGHT_ASSIGN = 354,
    AND_ASSIGN = 355,
    XOR_ASSIGN = 356,
    OR_ASSIGN = 357,
    SUB_ASSIGN = 358,
    LEFT_PAREN = 359,
    RIGHT_PAREN = 360,
    LEFT_BRACKET = 361,
    RIGHT_BRACKET = 362,
    LEFT_BRACE = 363,
    RIGHT_BRACE = 364,
    DOT = 365,
    COMMA = 366,
    COLON = 367,
    EQUAL = 368,
    SEMICOLON = 369,
    BANG = 370,
    DASH = 371,
    TILDE = 372,
    PLUS = 373,
    STAR = 374,
    SLASH = 375,
    PERCENT = 376,
    LEFT_ANGLE = 377,
    RIGHT_ANGLE = 378,
    VERTICAL_BAR = 379,
    CARET = 380,
    AMPERSAND = 381,
    QUESTION = 382
  };
#endif

/* Value type.  */
#if ! defined YYSTYPE && ! defined YYSTYPE_IS_DECLARED

union YYSTYPE
{


    struct {
        union {
            TString *string;
            float f;
            int i;
            unsigned int u;
            bool b;
        };
        TSymbol* symbol;
    } lex;
    struct {
        TOperator op;
        union {
            TIntermNode* intermNode;
            TIntermNodePair nodePair;
            TIntermTyped* intermTypedNode;
            TIntermAggregate* intermAggregate;
            TIntermSwitch* intermSwitch;
            TIntermCase* intermCase;
        };
        union {
            TPublicType type;
            TPrecision precision;
            TLayoutQualifier layoutQualifier;
            TQualifier qualifier;
            TFunction* function;
            TParameter param;
            TField* field;
            TFieldList* fieldList;
        };
    } interm;


};

typedef union YYSTYPE YYSTYPE;
# define YYSTYPE_IS_TRIVIAL 1
# define YYSTYPE_IS_DECLARED 1
#endif

/* Location type.  */
#if ! defined YYLTYPE && ! defined YYLTYPE_IS_DECLARED
typedef struct YYLTYPE YYLTYPE;
struct YYLTYPE
{
  int first_line;
  int first_column;
  int last_line;
  int last_column;
};
# define YYLTYPE_IS_DECLARED 1
# define YYLTYPE_IS_TRIVIAL 1
#endif



int yyparse (TParseContext* context, void *scanner);

#endif /* !YY_YY_GLSLANG_TAB_H_INCLUDED  */

/* Copy the second part of user declarations.  */


extern int yylex(YYSTYPE* yylval, YYLTYPE* yylloc, void* yyscanner);
extern void yyerror(YYLTYPE* yylloc, TParseContext* context, void *scanner, const char* reason);

#define YYLLOC_DEFAULT(Current, Rhs, N)                      \
  do {                                                       \
      if (N) {                                         \
        (Current).first_file = YYRHSLOC(Rhs, 1).first_file;  \
        (Current).first_line = YYRHSLOC(Rhs, 1).first_line;  \
        (Current).last_file = YYRHSLOC(Rhs, N).last_file;    \
        (Current).last_line = YYRHSLOC(Rhs, N).last_line;    \
      }                                                      \
      else {                                                 \
        (Current).first_file = YYRHSLOC(Rhs, 0).last_file;   \
        (Current).first_line = YYRHSLOC(Rhs, 0).last_line;   \
        (Current).last_file = YYRHSLOC(Rhs, 0).last_file;    \
        (Current).last_line = YYRHSLOC(Rhs, 0).last_line;    \
      }                                                      \
  } while (0)

#define VERTEX_ONLY(S, L) {  \
    if (context->getShaderType() != GL_VERTEX_SHADER) {  \
        context->error(L, " supported in vertex shaders only ", S);  \
        context->recover();  \
    }  \
}

#define FRAG_ONLY(S, L) {  \
    if (context->getShaderType() != GL_FRAGMENT_SHADER) {  \
        context->error(L, " supported in fragment shaders only ", S);  \
        context->recover();  \
    }  \
}

#define ES2_ONLY(S, L) {  \
    if (context->getShaderVersion() != 100) {  \
        context->error(L, " supported in GLSL ES 1.00 only ", S);  \
        context->recover();  \
    }  \
}

#define ES3_ONLY(TOKEN, LINE, REASON) {  \
    if (context->getShaderVersion() != 300) {  \
        context->error(LINE, REASON " supported in GLSL ES 3.00 only ", TOKEN);  \
        context->recover();  \
    }  \
}



#ifdef short
# undef short
#endif

#ifdef YYTYPE_UINT8
typedef YYTYPE_UINT8 yytype_uint8;
#else
typedef unsigned char yytype_uint8;
#endif

#ifdef YYTYPE_INT8
typedef YYTYPE_INT8 yytype_int8;
#else
typedef signed char yytype_int8;
#endif

#ifdef YYTYPE_UINT16
typedef YYTYPE_UINT16 yytype_uint16;
#else
typedef unsigned short int yytype_uint16;
#endif

#ifdef YYTYPE_INT16
typedef YYTYPE_INT16 yytype_int16;
#else
typedef short int yytype_int16;
#endif

#ifndef YYSIZE_T
# ifdef __SIZE_TYPE__
#  define YYSIZE_T __SIZE_TYPE__
# elif defined size_t
#  define YYSIZE_T size_t
# elif ! defined YYSIZE_T
#  include <stddef.h> /* INFRINGES ON USER NAME SPACE */
#  define YYSIZE_T size_t
# else
#  define YYSIZE_T unsigned int
# endif
#endif

#define YYSIZE_MAXIMUM ((YYSIZE_T) -1)

#ifndef YY_
# if defined YYENABLE_NLS && YYENABLE_NLS
#  if ENABLE_NLS
#   include <libintl.h> /* INFRINGES ON USER NAME SPACE */
#   define YY_(Msgid) dgettext ("bison-runtime", Msgid)
#  endif
# endif
# ifndef YY_
#  define YY_(Msgid) Msgid
# endif
#endif

#ifndef YY_ATTRIBUTE
# if (defined __GNUC__                                               \
      && (2 < __GNUC__ || (__GNUC__ == 2 && 96 <= __GNUC_MINOR__)))  \
     || defined __SUNPRO_C && 0x5110 <= __SUNPRO_C
#  define YY_ATTRIBUTE(Spec) __attribute__(Spec)
# else
#  define YY_ATTRIBUTE(Spec) /* empty */
# endif
#endif

#ifndef YY_ATTRIBUTE_PURE
# define YY_ATTRIBUTE_PURE   YY_ATTRIBUTE ((__pure__))
#endif

#ifndef YY_ATTRIBUTE_UNUSED
# define YY_ATTRIBUTE_UNUSED YY_ATTRIBUTE ((__unused__))
#endif

#if !defined _Noreturn \
     && (!defined __STDC_VERSION__ || __STDC_VERSION__ < 201112)
# if defined _MSC_VER && 1200 <= _MSC_VER
#  define _Noreturn __declspec (noreturn)
# else
#  define _Noreturn YY_ATTRIBUTE ((__noreturn__))
# endif
#endif

/* Suppress unused-variable warnings by "using" E.  */
#if ! defined lint || defined __GNUC__
# define YYUSE(E) ((void) (E))
#else
# define YYUSE(E) /* empty */
#endif

#if defined __GNUC__ && 407 <= __GNUC__ * 100 + __GNUC_MINOR__
/* Suppress an incorrect diagnostic about yylval being uninitialized.  */
# define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN \
    _Pragma ("GCC diagnostic push") \
    _Pragma ("GCC diagnostic ignored \"-Wuninitialized\"")\
    _Pragma ("GCC diagnostic ignored \"-Wmaybe-uninitialized\"")
# define YY_IGNORE_MAYBE_UNINITIALIZED_END \
    _Pragma ("GCC diagnostic pop")
#else
# define YY_INITIAL_VALUE(Value) Value
#endif
#ifndef YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_END
#endif
#ifndef YY_INITIAL_VALUE
# define YY_INITIAL_VALUE(Value) /* Nothing. */
#endif


#if ! defined yyoverflow || YYERROR_VERBOSE

/* The parser invokes alloca or malloc; define the necessary symbols.  */

# ifdef YYSTACK_USE_ALLOCA
#  if YYSTACK_USE_ALLOCA
#   ifdef __GNUC__
#    define YYSTACK_ALLOC __builtin_alloca
#   elif defined __BUILTIN_VA_ARG_INCR
#    include <alloca.h> /* INFRINGES ON USER NAME SPACE */
#   elif defined _AIX
#    define YYSTACK_ALLOC __alloca
#   elif defined _MSC_VER
#    include <malloc.h> /* INFRINGES ON USER NAME SPACE */
#    define alloca _alloca
#   else
#    define YYSTACK_ALLOC alloca
#    if ! defined _ALLOCA_H && ! defined EXIT_SUCCESS
#     include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
      /* Use EXIT_SUCCESS as a witness for stdlib.h.  */
#     ifndef EXIT_SUCCESS
#      define EXIT_SUCCESS 0
#     endif
#    endif
#   endif
#  endif
# endif

# ifdef YYSTACK_ALLOC
   /* Pacify GCC's 'empty if-body' warning.  */
#  define YYSTACK_FREE(Ptr) do { /* empty */; } while (0)
#  ifndef YYSTACK_ALLOC_MAXIMUM
    /* The OS might guarantee only one guard page at the bottom of the stack,
       and a page size can be as small as 4096 bytes.  So we cannot safely
       invoke alloca (N) if N exceeds 4096.  Use a slightly smaller number
       to allow for a few compiler-allocated temporary stack slots.  */
#   define YYSTACK_ALLOC_MAXIMUM 4032 /* reasonable circa 2006 */
#  endif
# else
#  define YYSTACK_ALLOC YYMALLOC
#  define YYSTACK_FREE YYFREE
#  ifndef YYSTACK_ALLOC_MAXIMUM
#   define YYSTACK_ALLOC_MAXIMUM YYSIZE_MAXIMUM
#  endif
#  if (defined __cplusplus && ! defined EXIT_SUCCESS \
       && ! ((defined YYMALLOC || defined malloc) \
             && (defined YYFREE || defined free)))
#   include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
#   ifndef EXIT_SUCCESS
#    define EXIT_SUCCESS 0
#   endif
#  endif
#  ifndef YYMALLOC
#   define YYMALLOC malloc
#   if ! defined malloc && ! defined EXIT_SUCCESS
void *malloc (YYSIZE_T); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
#  ifndef YYFREE
#   define YYFREE free
#   if ! defined free && ! defined EXIT_SUCCESS
void free (void *); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
# endif
#endif /* ! defined yyoverflow || YYERROR_VERBOSE */


#if (! defined yyoverflow \
     && (! defined __cplusplus \
         || (defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL \
             && defined YYSTYPE_IS_TRIVIAL && YYSTYPE_IS_TRIVIAL)))

/* A type that is properly aligned for any stack member.  */
union yyalloc
{
  yytype_int16 yyss_alloc;
  YYSTYPE yyvs_alloc;
  YYLTYPE yyls_alloc;
};

/* The size of the maximum gap between one aligned stack and the next.  */
# define YYSTACK_GAP_MAXIMUM (sizeof (union yyalloc) - 1)

/* The size of an array large to enough to hold all stacks, each with
   N elements.  */
# define YYSTACK_BYTES(N) \
     ((N) * (sizeof (yytype_int16) + sizeof (YYSTYPE) + sizeof (YYLTYPE)) \
      + 2 * YYSTACK_GAP_MAXIMUM)

# define YYCOPY_NEEDED 1

/* Relocate STACK from its old location to the new one.  The
   local variables YYSIZE and YYSTACKSIZE give the old and new number of
   elements in the stack, and YYPTR gives the new location of the
   stack.  Advance YYPTR to a properly aligned location for the next
   stack.  */
# define YYSTACK_RELOCATE(Stack_alloc, Stack)                           \
    do                                                                  \
      {                                                                 \
        YYSIZE_T yynewbytes;                                            \
        YYCOPY (&yyptr->Stack_alloc, Stack, yysize);                    \
        Stack = &yyptr->Stack_alloc;                                    \
        yynewbytes = yystacksize * sizeof (*Stack) + YYSTACK_GAP_MAXIMUM; \
        yyptr += yynewbytes / sizeof (*yyptr);                          \
      }                                                                 \
    while (0)

#endif

#if defined YYCOPY_NEEDED && YYCOPY_NEEDED
/* Copy COUNT objects from SRC to DST.  The source and destination do
   not overlap.  */
# ifndef YYCOPY
#  if defined __GNUC__ && 1 < __GNUC__
#   define YYCOPY(Dst, Src, Count) \
      __builtin_memcpy (Dst, Src, (Count) * sizeof (*(Src)))
#  else
#   define YYCOPY(Dst, Src, Count)              \
      do                                        \
        {                                       \
          YYSIZE_T yyi;                         \
          for (yyi = 0; yyi < (Count); yyi++)   \
            (Dst)[yyi] = (Src)[yyi];            \
        }                                       \
      while (0)
#  endif
# endif
#endif /* !YYCOPY_NEEDED */

/* YYFINAL -- State number of the termination state.  */
#define YYFINAL  116
/* YYLAST -- Last index in YYTABLE.  */
#define YYLAST   2516

/* YYNTOKENS -- Number of terminals.  */
#define YYNTOKENS  128
/* YYNNTS -- Number of nonterminals.  */
#define YYNNTS  94
/* YYNRULES -- Number of rules.  */
#define YYNRULES  275
/* YYNSTATES -- Number of states.  */
#define YYNSTATES  417

/* YYTRANSLATE[YYX] -- Symbol number corresponding to YYX as returned
   by yylex, with out-of-bounds checking.  */
#define YYUNDEFTOK  2
#define YYMAXUTOK   382

#define YYTRANSLATE(YYX)                                                \
  ((unsigned int) (YYX) <= YYMAXUTOK ? yytranslate[YYX] : YYUNDEFTOK)

/* YYTRANSLATE[TOKEN-NUM] -- Symbol number corresponding to TOKEN-NUM
   as returned by yylex, without out-of-bounds checking.  */
static const yytype_uint8 yytranslate[] =
{
       0,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     1,     2,     3,     4,
       5,     6,     7,     8,     9,    10,    11,    12,    13,    14,
      15,    16,    17,    18,    19,    20,    21,    22,    23,    24,
      25,    26,    27,    28,    29,    30,    31,    32,    33,    34,
      35,    36,    37,    38,    39,    40,    41,    42,    43,    44,
      45,    46,    47,    48,    49,    50,    51,    52,    53,    54,
      55,    56,    57,    58,    59,    60,    61,    62,    63,    64,
      65,    66,    67,    68,    69,    70,    71,    72,    73,    74,
      75,    76,    77,    78,    79,    80,    81,    82,    83,    84,
      85,    86,    87,    88,    89,    90,    91,    92,    93,    94,
      95,    96,    97,    98,    99,   100,   101,   102,   103,   104,
     105,   106,   107,   108,   109,   110,   111,   112,   113,   114,
     115,   116,   117,   118,   119,   120,   121,   122,   123,   124,
     125,   126,   127
};

#if YYDEBUG
  /* YYRLINE[YYN] -- Source line where rule number YYN was defined.  */
static const yytype_uint16 yyrline[] =
{
       0,   212,   212,   213,   216,   226,   229,   234,   239,   244,
     249,   255,   258,   261,   264,   267,   270,   276,   284,   295,
     299,   307,   310,   316,   320,   327,   333,   342,   350,   356,
     363,   373,   376,   379,   382,   392,   393,   394,   395,   403,
     404,   407,   410,   417,   418,   421,   427,   428,   432,   439,
     440,   443,   446,   449,   455,   456,   459,   465,   466,   473,
     474,   481,   482,   489,   490,   496,   497,   503,   504,   510,
     511,   517,   518,   526,   527,   528,   529,   533,   534,   535,
     539,   543,   547,   551,   558,   561,   567,   575,   583,   586,
     592,   603,   607,   611,   615,   622,   628,   631,   638,   646,
     667,   694,   704,   732,   737,   747,   752,   762,   765,   768,
     771,   777,   784,   787,   791,   795,   800,   805,   812,   816,
     820,   824,   829,   834,   838,   845,   855,   861,   864,   870,
     876,   883,   892,   902,   910,   913,   920,   924,   928,   933,
     941,   944,   948,   952,   961,   970,   978,   988,  1000,  1003,
    1006,  1012,  1019,  1022,  1028,  1031,  1034,  1040,  1043,  1048,
    1063,  1067,  1071,  1075,  1079,  1083,  1088,  1093,  1098,  1103,
    1108,  1113,  1118,  1123,  1128,  1133,  1138,  1143,  1148,  1153,
    1158,  1163,  1168,  1173,  1178,  1183,  1188,  1192,  1196,  1200,
    1204,  1208,  1212,  1216,  1220,  1224,  1228,  1232,  1236,  1240,
    1244,  1248,  1256,  1264,  1268,  1281,  1281,  1284,  1284,  1290,
    1293,  1309,  1312,  1321,  1325,  1331,  1338,  1353,  1357,  1361,
    1362,  1368,  1369,  1370,  1371,  1372,  1373,  1374,  1378,  1379,
    1379,  1379,  1389,  1390,  1394,  1394,  1395,  1395,  1400,  1403,
    1413,  1416,  1422,  1423,  1427,  1435,  1439,  1446,  1446,  1453,
    1456,  1463,  1468,  1483,  1483,  1488,  1488,  1495,  1495,  1503,
    1506,  1512,  1515,  1521,  1525,  1532,  1535,  1538,  1541,  1544,
    1553,  1557,  1564,  1567,  1573,  1573
};
#endif

#if YYDEBUG || YYERROR_VERBOSE || 0
/* YYTNAME[SYMBOL-NUM] -- String name of the symbol SYMBOL-NUM.
   First, the terminals, then, starting at YYNTOKENS, nonterminals.  */
static const char *const yytname[] =
{
  "$end", "error", "$undefined", "INVARIANT", "HIGH_PRECISION",
  "MEDIUM_PRECISION", "LOW_PRECISION", "PRECISION", "ATTRIBUTE",
  "CONST_QUAL", "BOOL_TYPE", "FLOAT_TYPE", "INT_TYPE", "UINT_TYPE",
  "BREAK", "CONTINUE", "DO", "ELSE", "FOR", "IF", "DISCARD", "RETURN",
  "SWITCH", "CASE", "DEFAULT", "BVEC2", "BVEC3", "BVEC4", "IVEC2", "IVEC3",
  "IVEC4", "VEC2", "VEC3", "VEC4", "UVEC2", "UVEC3", "UVEC4", "MATRIX2",
  "MATRIX3", "MATRIX4", "IN_QUAL", "OUT_QUAL", "INOUT_QUAL", "UNIFORM",
  "VARYING", "MATRIX2x3", "MATRIX3x2", "MATRIX2x4", "MATRIX4x2",
  "MATRIX3x4", "MATRIX4x3", "CENTROID", "FLAT", "SMOOTH", "STRUCT",
  "VOID_TYPE", "WHILE", "SAMPLER2D", "SAMPLERCUBE", "SAMPLER_EXTERNAL_OES",
  "SAMPLER2DRECT", "SAMPLER2DARRAY", "ISAMPLER2D", "ISAMPLER3D",
  "ISAMPLERCUBE", "ISAMPLER2DARRAY", "USAMPLER2D", "USAMPLER3D",
  "USAMPLERCUBE", "USAMPLER2DARRAY", "SAMPLER3D", "SAMPLER3DRECT",
  "SAMPLER2DSHADOW", "SAMPLERCUBESHADOW", "SAMPLER2DARRAYSHADOW", "LAYOUT",
  "IDENTIFIER", "TYPE_NAME", "FLOATCONSTANT", "INTCONSTANT",
  "UINTCONSTANT", "BOOLCONSTANT", "FIELD_SELECTION", "LEFT_OP", "RIGHT_OP",
  "INC_OP", "DEC_OP", "LE_OP", "GE_OP", "EQ_OP", "NE_OP", "AND_OP",
  "OR_OP", "XOR_OP", "MUL_ASSIGN", "DIV_ASSIGN", "ADD_ASSIGN",
  "MOD_ASSIGN", "LEFT_ASSIGN", "RIGHT_ASSIGN", "AND_ASSIGN", "XOR_ASSIGN",
  "OR_ASSIGN", "SUB_ASSIGN", "LEFT_PAREN", "RIGHT_PAREN", "LEFT_BRACKET",
  "RIGHT_BRACKET", "LEFT_BRACE", "RIGHT_BRACE", "DOT", "COMMA", "COLON",
  "EQUAL", "SEMICOLON", "BANG", "DASH", "TILDE", "PLUS", "STAR", "SLASH",
  "PERCENT", "LEFT_ANGLE", "RIGHT_ANGLE", "VERTICAL_BAR", "CARET",
  "AMPERSAND", "QUESTION", "$accept", "identifier", "variable_identifier",
  "primary_expression", "postfix_expression", "integer_expression",
  "function_call", "function_call_or_method", "function_call_generic",
  "function_call_header_no_parameters",
  "function_call_header_with_parameters", "function_call_header",
  "function_identifier", "unary_expression", "unary_operator",
  "multiplicative_expression", "additive_expression", "shift_expression",
  "relational_expression", "equality_expression", "and_expression",
  "exclusive_or_expression", "inclusive_or_expression",
  "logical_and_expression", "logical_xor_expression",
  "logical_or_expression", "conditional_expression",
  "assignment_expression", "assignment_operator", "expression",
  "constant_expression", "enter_struct", "declaration",
  "function_prototype", "function_declarator",
  "function_header_with_parameters", "function_header",
  "parameter_declarator", "parameter_declaration", "parameter_qualifier",
  "parameter_type_specifier", "init_declarator_list", "single_declaration",
  "fully_specified_type", "interpolation_qualifier",
  "parameter_type_qualifier", "type_qualifier", "storage_qualifier",
  "type_specifier", "precision_qualifier", "layout_qualifier",
  "layout_qualifier_id_list", "layout_qualifier_id",
  "type_specifier_no_prec", "type_specifier_nonarray", "struct_specifier",
  "$@1", "$@2", "struct_declaration_list", "struct_declaration",
  "struct_declarator_list", "struct_declarator", "initializer",
  "declaration_statement", "statement", "simple_statement",
  "compound_statement", "$@3", "$@4", "statement_no_new_scope",
  "statement_with_scope", "$@5", "$@6", "compound_statement_no_new_scope",
  "statement_list", "expression_statement", "selection_statement",
  "selection_rest_statement", "switch_statement", "$@7", "case_label",
  "condition", "iteration_statement", "$@8", "$@9", "$@10",
  "for_init_statement", "conditionopt", "for_rest_statement",
  "jump_statement", "translation_unit", "external_declaration",
  "function_definition", "$@11", YY_NULLPTR
};
#endif

# ifdef YYPRINT
/* YYTOKNUM[NUM] -- (External) token number corresponding to the
   (internal) symbol number NUM (which must be that of a token).  */
static const yytype_uint16 yytoknum[] =
{
       0,   256,   257,   258,   259,   260,   261,   262,   263,   264,
     265,   266,   267,   268,   269,   270,   271,   272,   273,   274,
     275,   276,   277,   278,   279,   280,   281,   282,   283,   284,
     285,   286,   287,   288,   289,   290,   291,   292,   293,   294,
     295,   296,   297,   298,   299,   300,   301,   302,   303,   304,
     305,   306,   307,   308,   309,   310,   311,   312,   313,   314,
     315,   316,   317,   318,   319,   320,   321,   322,   323,   324,
     325,   326,   327,   328,   329,   330,   331,   332,   333,   334,
     335,   336,   337,   338,   339,   340,   341,   342,   343,   344,
     345,   346,   347,   348,   349,   350,   351,   352,   353,   354,
     355,   356,   357,   358,   359,   360,   361,   362,   363,   364,
     365,   366,   367,   368,   369,   370,   371,   372,   373,   374,
     375,   376,   377,   378,   379,   380,   381,   382
};
# endif

#define YYPACT_NINF -361

#define yypact_value_is_default(Yystate) \
  (!!((Yystate) == (-361)))

#define YYTABLE_NINF -235

#define yytable_value_is_error(Yytable_value) \
  0

  /* YYPACT[STATE-NUM] -- Index in YYTABLE of the portion describing
     STATE-NUM.  */
static const yytype_int16 yypact[] =
{
    2154,   224,  -361,  -361,  -361,   130,  -361,  -361,  -361,  -361,
    -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,
    -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,
    -361,  -361,  -361,  -361,  -361,  -361,  -361,   145,  -361,  -361,
     -46,  -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,
    -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,   -82,
    -361,  -361,   -68,   -41,   -45,     9,     7,  -361,   117,    16,
    1173,  -361,  -361,  2439,    16,  -361,    -9,  -361,  2079,  -361,
    -361,  -361,  -361,    16,  -361,  2439,  -361,  -361,  -361,  -361,
    -361,   -31,    23,  -361,    11,  -361,    63,  -361,  -361,  -361,
    -361,  -361,  2303,   168,   120,  -361,    13,   -66,  -361,    31,
    -361,  2229,  -361,  -361,  -361,  1243,  -361,  -361,  -361,    56,
    2229,  -361,    17,   -50,  -361,   401,  -361,  -361,  -361,  -361,
     120,  2303,   -18,  -361,  1341,  1632,  -361,   179,  2303,   120,
    1824,  -361,    70,  -361,  -361,  -361,  -361,  -361,  1632,  1632,
    1632,  -361,  -361,  -361,  -361,  -361,  -361,  -361,    22,  -361,
    -361,  -361,   101,   -29,  1727,   114,  -361,  1632,    96,   -97,
     128,   -54,   111,   118,   102,   115,   154,   153,   -69,  -361,
     140,  -361,  -361,  1909,  2229,   124,  -361,    23,   134,   136,
    -361,   147,   149,   143,  1439,   155,  1632,   148,   157,   160,
    -361,  -361,   184,  -361,  -361,    52,  -361,   -68,   158,  -361,
    -361,  -361,  -361,   517,  -361,  -361,  -361,  -361,  -361,  -361,
     166,  -361,  -361,  1534,  1632,   150,   159,  -361,  -361,   120,
     167,    61,  -361,   -62,  -361,  -361,  -361,    -5,  -361,  -361,
    1632,  2371,  -361,  -361,  1632,   185,  -361,  -361,  -361,  1632,
    1632,  1632,  1632,  1632,  1632,  1632,  1632,  1632,  1632,  1632,
    1632,  1632,  1632,  1632,  1632,  1632,  1632,  1632,  1632,  -361,
    -361,  1994,  -361,  -361,  -361,  -361,  -361,   181,  -361,  1632,
    -361,  -361,    67,  1632,   180,  -361,  -361,  -361,   633,  -361,
    -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,
    1632,  1632,  -361,  -361,  -361,  1632,   178,   186,  -361,  1632,
     182,    68,  1632,   120,  -361,   -71,  -361,  -361,   187,   188,
    -361,   192,  -361,  -361,  -361,  -361,  -361,    96,    96,   -97,
     -97,   128,   128,   128,   128,   -54,   -54,   111,   118,   102,
     115,   154,   153,   113,  -361,   242,    11,   865,   981,     4,
    -361,    18,  -361,  1078,   633,  -361,  -361,   194,  1632,   189,
    -361,  1632,  -361,   196,  -361,  1632,  -361,  -361,  1632,   200,
    -361,  -361,  -361,  -361,  1078,   181,  -361,   188,   120,  2303,
     201,   198,  -361,  -361,  1632,  -361,  -361,   202,  -361,  1632,
    -361,   191,   203,   293,  -361,   204,   205,   749,  -361,  -361,
     197,    60,  1632,   749,   181,  -361,  1632,  -361,  -361,  -361,
    -361,   199,   188,  -361,  -361,  -361,  -361
};

  /* YYDEFACT[STATE-NUM] -- Default reduction number in state STATE-NUM.
     Performed when YYTABLE does not specify something else to do.  Zero
     means the default is an error.  */
static const yytype_uint16 yydefact[] =
{
       0,     0,   148,   149,   150,     0,   130,   140,   164,   161,
     162,   163,   168,   169,   170,   171,   172,   173,   165,   166,
     167,   174,   175,   176,   177,   178,   179,   141,   142,   145,
     131,   180,   181,   182,   183,   184,   185,     0,   128,   127,
       0,   160,   186,   188,   201,   202,   189,   190,   191,   192,
     193,   194,   195,   196,   197,   187,   198,   199,   200,     0,
     204,   273,   274,     0,    97,   107,     0,   112,   118,   135,
       0,   133,   125,     0,   136,   146,   157,   203,     0,   270,
     272,   132,   124,     0,   138,     0,   143,   144,     2,     3,
     207,     0,     0,    88,     0,    95,   107,   129,   108,   109,
     110,    98,     0,   107,     0,    89,     2,   119,   134,     0,
      94,     0,   126,   147,   137,     0,     1,   271,   139,     0,
       0,   205,   154,     0,   152,     0,   275,    99,   104,   106,
     111,     0,   113,   100,     0,     0,    87,     0,     0,     0,
       0,   209,     4,     8,     6,     7,     9,    30,     0,     0,
       0,   158,    37,    36,    38,    35,     5,    11,    31,    13,
      18,    19,     0,     0,    24,     0,    39,     0,    43,    46,
      49,    54,    57,    59,    61,    63,    65,    67,    69,    86,
       0,    28,    90,     0,     0,     0,   151,     0,     0,     0,
     255,     0,     0,     0,     0,     0,     0,     0,     0,   229,
     238,   242,    39,    71,    84,     0,   218,     0,   146,   221,
     240,   220,   219,     0,   222,   223,   224,   225,   226,   227,
     101,   103,   105,     0,     0,     0,     0,   217,   123,     0,
     215,     0,   213,     0,   210,    32,    33,     0,    15,    16,
       0,     0,    22,    21,     0,   160,    25,    27,    34,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,   159,
     208,     0,   155,   156,   153,   266,   265,   236,   257,     0,
     269,   267,     0,     0,     0,   250,   253,   228,     0,    74,
      75,    77,    76,    79,    80,    81,    82,    83,    78,    73,
       0,     0,   243,   239,   241,     0,     0,     0,   117,     0,
     120,     0,     0,     0,   211,     0,    91,    10,     0,    17,
      29,    14,    20,    26,    40,    41,    42,    45,    44,    47,
      48,    52,    53,    50,    51,    55,    56,    58,    60,    62,
      64,    66,    68,     0,   206,     0,     0,     0,     0,     0,
     268,     0,   249,     0,   230,    72,    85,     0,     0,   114,
     121,     0,   212,     0,   214,     0,    92,    12,     0,     0,
     235,   237,   260,   259,   262,   236,   247,   251,     0,     0,
       0,     0,   102,   115,     0,   122,   216,     0,    70,     0,
     261,     0,     0,   246,   244,     0,     0,     0,   231,   116,
       0,     0,   263,     0,   236,   248,     0,   233,   254,   232,
      93,     0,   264,   258,   245,   252,   256
};

  /* YYPGOTO[NTERM-NUM].  */
static const yytype_int16 yypgoto[] =
{
    -361,   -39,  -361,  -361,  -361,  -361,  -361,  -361,    73,  -361,
    -361,  -361,  -361,  -107,  -361,   -15,   -20,   -67,   -19,    53,
      54,    55,    51,    57,    58,  -361,  -110,  -132,  -361,  -146,
    -125,  -361,     6,    12,  -361,  -361,  -361,   190,   228,   217,
     195,  -361,  -361,  -336,    25,  -361,  -104,    27,   -57,   322,
    -361,  -361,   141,     0,  -361,  -361,  -361,  -361,  -109,  -130,
     100,    19,  -185,   -17,  -193,  -310,   -65,  -361,  -361,   -70,
    -360,  -361,  -361,   -92,    46,   -13,  -361,  -361,  -361,  -361,
    -361,   -38,  -361,  -361,  -361,  -361,  -361,  -361,  -361,  -361,
    -361,   259,  -361,  -361
};

  /* YYDEFGOTO[NTERM-NUM].  */
static const yytype_int16 yydefgoto[] =
{
      -1,   230,   156,   157,   158,   318,   159,   160,   161,   162,
     163,   164,   165,   202,   167,   168,   169,   170,   171,   172,
     173,   174,   175,   176,   177,   178,   203,   204,   300,   205,
     180,   111,   206,   207,    63,    64,    65,   128,   101,   102,
     129,    66,    67,    68,    69,   103,    70,    71,    72,    73,
      74,   123,   124,   181,    76,    77,   184,   120,   140,   141,
     231,   232,   228,   209,   210,   211,   212,   288,   381,   408,
     345,   346,   347,   409,   213,   214,   215,   394,   216,   395,
     217,   380,   218,   353,   277,   348,   374,   391,   392,   219,
      78,    79,    80,    94
};

  /* YYTABLE[YYPACT[STATE-NUM]] -- What to do in state STATE-NUM.  If
     positive, shift that token.  If negative, reduce the rule whose
     number is the opposite.  If YYTABLE_NINF, syntax error.  */
static const yytype_int16 yytable[] =
{
      75,    91,   126,   227,   237,   179,    61,   138,   166,   226,
     234,   183,    62,   112,   315,   393,   138,   378,    97,   252,
     304,   253,    92,   267,   179,     7,    83,   166,    84,   107,
      88,    89,   246,   256,   257,   365,   138,   371,   378,   308,
     134,   235,   236,   366,   414,   130,    93,   135,   282,    98,
      99,   100,   316,   234,   139,   186,    27,    28,   268,    29,
     248,   187,    90,   139,    95,   132,    96,    37,   258,   259,
      75,   284,    97,   113,   130,   271,   243,   121,    75,   138,
     138,   229,   244,   139,    61,   119,   179,   407,   223,   166,
      62,   220,   227,   407,   319,   224,   108,   115,   307,   122,
     317,   114,    75,    98,    99,   100,   301,   238,   239,   375,
     118,    75,   323,   179,   -96,   301,   166,   133,   104,   125,
      75,   105,   343,   376,   360,   208,   139,   139,   240,   301,
     185,    75,   241,   349,     2,     3,     4,   351,    75,   136,
      75,   234,   324,   325,   326,   166,   166,   166,   166,   166,
     166,   166,   166,   166,   166,   166,   166,   166,   166,   166,
     166,   304,    83,   301,    84,   411,   302,   138,   355,   356,
     182,   301,   313,   383,   -29,   314,   385,   227,   301,   313,
     357,   350,   362,    75,    75,    86,    87,   363,     7,   331,
     332,   333,   334,   106,    89,   179,    88,    89,   166,   399,
     260,   261,   179,   272,   273,   166,   242,   377,    98,    99,
     100,   254,   255,   208,   139,   249,   250,   251,   247,    27,
      28,   415,    29,    81,   301,   368,   227,   263,   377,   227,
      37,    38,    39,     7,   329,   330,   388,   327,   328,   264,
     387,   335,   336,   401,   262,   265,   266,   269,   275,   379,
     276,   278,   227,   279,   370,   179,   412,   280,   166,   283,
     285,   286,   -28,   309,    27,    28,   310,    29,    81,   287,
     379,    75,   305,   312,   227,    37,    38,    39,   289,   290,
     291,   292,   293,   294,   295,   296,   297,   298,   208,  -234,
     -23,   358,   352,   359,   367,   361,   -30,   299,   369,   301,
      82,   382,   384,   386,   389,   402,   397,   398,   403,   400,
     404,   410,   199,   416,   322,   337,   340,   338,   406,   339,
     131,   221,   112,   341,   127,   342,   222,    85,   274,   311,
     405,   372,   364,   413,   354,   373,   390,   117,     0,   396,
       0,     0,     0,     0,     0,     0,     0,   208,   208,     0,
       0,     0,     0,   208,   208,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,   208,     0,     0,     0,     0,    75,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,   208,     0,     0,
       0,     0,     0,   208,     1,     2,     3,     4,     5,     6,
       7,     8,     9,    10,    11,   188,   189,   190,     0,   191,
     192,   193,   194,   195,   196,   197,    12,    13,    14,    15,
      16,    17,    18,    19,    20,    21,    22,    23,    24,    25,
      26,    27,    28,     0,    29,    30,    31,    32,    33,    34,
      35,    36,    37,    38,    39,    40,    41,   198,    42,    43,
      44,    45,    46,    47,    48,    49,    50,    51,    52,    53,
      54,    55,     0,    56,    57,    58,    59,   142,    60,   143,
     144,   145,   146,   147,     0,     0,   148,   149,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,   150,     0,     0,     0,   199,
     200,     0,     0,     0,     0,   201,   152,   153,   154,   155,
       1,     2,     3,     4,     5,     6,     7,     8,     9,    10,
      11,   188,   189,   190,     0,   191,   192,   193,   194,   195,
     196,   197,    12,    13,    14,    15,    16,    17,    18,    19,
      20,    21,    22,    23,    24,    25,    26,    27,    28,     0,
      29,    30,    31,    32,    33,    34,    35,    36,    37,    38,
      39,    40,    41,   198,    42,    43,    44,    45,    46,    47,
      48,    49,    50,    51,    52,    53,    54,    55,     0,    56,
      57,    58,    59,   142,    60,   143,   144,   145,   146,   147,
       0,     0,   148,   149,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,   150,     0,     0,     0,   199,   303,     0,     0,     0,
       0,   201,   152,   153,   154,   155,     1,     2,     3,     4,
       5,     6,     7,     8,     9,    10,    11,   188,   189,   190,
       0,   191,   192,   193,   194,   195,   196,   197,    12,    13,
      14,    15,    16,    17,    18,    19,    20,    21,    22,    23,
      24,    25,    26,    27,    28,     0,    29,    30,    31,    32,
      33,    34,    35,    36,    37,    38,    39,    40,    41,   198,
      42,    43,    44,    45,    46,    47,    48,    49,    50,    51,
      52,    53,    54,    55,     0,    56,    57,    58,    59,   142,
      60,   143,   144,   145,   146,   147,     0,     0,   148,   149,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,   150,     0,     0,
       0,   199,     0,     0,     0,     0,     0,   201,   152,   153,
     154,   155,     1,     2,     3,     4,     5,     6,     7,     8,
       9,    10,    11,   188,   189,   190,     0,   191,   192,   193,
     194,   195,   196,   197,    12,    13,    14,    15,    16,    17,
      18,    19,    20,    21,    22,    23,    24,    25,    26,    27,
      28,     0,    29,    30,    31,    32,    33,    34,    35,    36,
      37,    38,    39,    40,    41,   198,    42,    43,    44,    45,
      46,    47,    48,    49,    50,    51,    52,    53,    54,    55,
       0,    56,    57,    58,    59,   142,    60,   143,   144,   145,
     146,   147,     0,     0,   148,   149,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,   150,     0,     0,     0,   125,     0,     0,
       0,     0,     0,   201,   152,   153,   154,   155,     1,     2,
       3,     4,     5,     6,     7,     8,     9,    10,    11,   188,
     189,   190,     0,   191,   192,   193,   194,   195,   196,   197,
      12,    13,    14,    15,    16,    17,    18,    19,    20,    21,
      22,    23,    24,    25,    26,    27,    28,     0,    29,    30,
      31,    32,    33,    34,    35,    36,    37,    38,    39,    40,
      41,   198,    42,    43,    44,    45,    46,    47,    48,    49,
      50,    51,    52,    53,    54,    55,     0,    56,    57,    58,
      59,   142,    60,   143,   144,   145,   146,   147,     0,     0,
     148,   149,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,   150,
       0,     0,     0,     0,     0,     0,     0,     0,     0,   201,
     152,   153,   154,   155,     1,     2,     3,     4,     5,     6,
       7,     8,     9,    10,    11,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,    12,    13,    14,    15,
      16,    17,    18,    19,    20,    21,    22,    23,    24,    25,
      26,    27,    28,     0,    29,    30,    31,    32,    33,    34,
      35,    36,    37,    38,    39,    40,    41,     0,    42,    43,
      44,    45,    46,    47,    48,    49,    50,    51,    52,    53,
      54,    55,     0,    56,    57,    58,    59,   142,    60,   143,
     144,   145,   146,   147,     0,     0,   148,   149,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,   137,     2,     3,     4,   150,     6,     7,     8,     9,
      10,    11,     0,     0,     0,   201,   152,   153,   154,   155,
       0,     0,     0,    12,    13,    14,    15,    16,    17,    18,
      19,    20,    21,    22,    23,    24,    25,    26,    27,    28,
       0,    29,    30,    31,    32,    33,    34,    35,    36,    37,
      38,    39,    40,    41,     0,    42,    43,    44,    45,    46,
      47,    48,    49,    50,    51,    52,    53,    54,    55,     0,
      56,    57,    58,    59,   142,    60,   143,   144,   145,   146,
     147,     0,     0,   148,   149,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     2,     3,     4,
       0,     0,   150,     8,     9,    10,    11,     0,     0,     0,
       0,     0,     0,   152,   153,   154,   155,     0,    12,    13,
      14,    15,    16,    17,    18,    19,    20,    21,    22,    23,
      24,    25,    26,     0,     0,     0,     0,     0,    31,    32,
      33,    34,    35,    36,     0,     0,     0,    40,    41,     0,
      42,    43,    44,    45,    46,    47,    48,    49,    50,    51,
      52,    53,    54,    55,     0,    56,    57,    58,     0,   109,
      60,     0,     0,     8,     9,    10,    11,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,    12,    13,
      14,    15,    16,    17,    18,    19,    20,    21,    22,    23,
      24,    25,    26,     0,     0,     0,     0,   110,    31,    32,
      33,    34,    35,    36,     0,     0,     0,    40,    41,     0,
      42,    43,    44,    45,    46,    47,    48,    49,    50,    51,
      52,    53,    54,    55,     0,    56,    57,    58,     0,   142,
      60,   143,   144,   145,   146,   147,     0,     0,   148,   149,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,   150,     0,     0,
     151,     8,     9,    10,    11,     0,     0,     0,   152,   153,
     154,   155,     0,     0,     0,     0,    12,    13,    14,    15,
      16,    17,    18,    19,    20,    21,    22,    23,    24,    25,
      26,     0,     0,     0,     0,     0,    31,    32,    33,    34,
      35,    36,     0,     0,     0,    40,    41,     0,    42,    43,
      44,    45,    46,    47,    48,    49,    50,    51,    52,    53,
      54,    55,     0,    56,    57,    58,     0,   142,    60,   143,
     144,   145,   146,   147,     0,     0,   148,   149,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,   150,     0,     0,   225,     8,
       9,    10,    11,     0,     0,     0,   152,   153,   154,   155,
       0,     0,     0,     0,    12,    13,    14,    15,    16,    17,
      18,    19,    20,    21,    22,    23,    24,    25,    26,     0,
       0,     0,     0,     0,    31,    32,    33,    34,    35,    36,
       0,     0,     0,    40,    41,     0,    42,    43,    44,    45,
      46,    47,    48,    49,    50,    51,    52,    53,    54,    55,
       0,    56,    57,    58,     0,   142,    60,   143,   144,   145,
     146,   147,     0,     0,   148,   149,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,   150,     8,     9,    10,    11,     0,     0,
       0,     0,     0,   281,   152,   153,   154,   155,     0,    12,
      13,    14,    15,    16,    17,    18,    19,    20,    21,    22,
      23,    24,    25,    26,     0,     0,     0,     0,     0,    31,
      32,    33,    34,    35,    36,     0,     0,     0,    40,    41,
       0,    42,    43,    44,    45,    46,    47,    48,    49,    50,
      51,    52,    53,    54,    55,     0,    56,    57,    58,     0,
     142,    60,   143,   144,   145,   146,   147,     0,     0,   148,
     149,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,   150,     0,
       0,   306,     8,     9,    10,    11,     0,     0,     0,   152,
     153,   154,   155,     0,     0,     0,     0,    12,    13,    14,
      15,    16,    17,    18,    19,    20,    21,    22,    23,    24,
      25,    26,     0,     0,     0,     0,     0,    31,    32,    33,
      34,    35,    36,     0,     0,     0,    40,    41,     0,    42,
      43,    44,    45,    46,    47,    48,    49,    50,    51,    52,
      53,    54,    55,     0,    56,    57,    58,     0,   142,    60,
     143,   144,   145,   146,   147,     0,     0,   148,   149,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,   150,     8,     9,    10,
      11,     0,     0,     0,     0,     0,     0,   152,   153,   154,
     155,     0,    12,    13,    14,    15,    16,    17,    18,    19,
      20,    21,    22,    23,    24,    25,    26,     0,     0,     0,
       0,     0,    31,    32,    33,    34,    35,    36,     0,     0,
       0,    40,   245,     0,    42,    43,    44,    45,    46,    47,
      48,    49,    50,    51,    52,    53,    54,    55,     0,    56,
      57,    58,     0,   142,    60,   143,   144,   145,   146,   147,
       0,     0,   148,   149,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,   137,     2,     3,
       4,   150,     6,     7,     8,     9,    10,    11,     0,     0,
       0,     0,   152,   153,   154,   155,     0,     0,     0,    12,
      13,    14,    15,    16,    17,    18,    19,    20,    21,    22,
      23,    24,    25,    26,    27,    28,     0,    29,    30,    31,
      32,    33,    34,    35,    36,    37,    38,    39,    40,    41,
       0,    42,    43,    44,    45,    46,    47,    48,    49,    50,
      51,    52,    53,    54,    55,     0,    56,    57,    58,    59,
       0,    60,     0,     0,     0,     0,     0,     0,     0,     0,
       0,     0,   137,     2,     3,     4,     0,     6,     7,     8,
       9,    10,    11,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,   233,    12,    13,    14,    15,    16,    17,
      18,    19,    20,    21,    22,    23,    24,    25,    26,    27,
      28,     0,    29,    30,    31,    32,    33,    34,    35,    36,
      37,    38,    39,    40,    41,     0,    42,    43,    44,    45,
      46,    47,    48,    49,    50,    51,    52,    53,    54,    55,
       0,    56,    57,    58,    59,     0,    60,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,   137,     2,     3,
       4,     0,     6,     7,     8,     9,    10,    11,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,   270,    12,
      13,    14,    15,    16,    17,    18,    19,    20,    21,    22,
      23,    24,    25,    26,    27,    28,     0,    29,    30,    31,
      32,    33,    34,    35,    36,    37,    38,    39,    40,    41,
       0,    42,    43,    44,    45,    46,    47,    48,    49,    50,
      51,    52,    53,    54,    55,     0,    56,    57,    58,    59,
       0,    60,     0,     0,     0,     0,     0,     0,     0,   116,
       0,     0,     1,     2,     3,     4,     5,     6,     7,     8,
       9,    10,    11,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,   344,    12,    13,    14,    15,    16,    17,
      18,    19,    20,    21,    22,    23,    24,    25,    26,    27,
      28,     0,    29,    30,    31,    32,    33,    34,    35,    36,
      37,    38,    39,    40,    41,     0,    42,    43,    44,    45,
      46,    47,    48,    49,    50,    51,    52,    53,    54,    55,
       0,    56,    57,    58,    59,     0,    60,     1,     2,     3,
       4,     5,     6,     7,     8,     9,    10,    11,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,    12,
      13,    14,    15,    16,    17,    18,    19,    20,    21,    22,
      23,    24,    25,    26,    27,    28,     0,    29,    30,    31,
      32,    33,    34,    35,    36,    37,    38,    39,    40,    41,
       0,    42,    43,    44,    45,    46,    47,    48,    49,    50,
      51,    52,    53,    54,    55,     0,    56,    57,    58,    59,
       0,    60,   137,     2,     3,     4,     0,     6,     7,     8,
       9,    10,    11,     0,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,    12,    13,    14,    15,    16,    17,
      18,    19,    20,    21,    22,    23,    24,    25,    26,    27,
      28,     0,    29,    30,    31,    32,    33,    34,    35,    36,
      37,    38,    39,    40,    41,     0,    42,    43,    44,    45,
      46,    47,    48,    49,    50,    51,    52,    53,    54,    55,
       0,    56,    57,    58,    59,     0,    60,     2,     3,     4,
       0,     0,     0,     8,     9,    10,    11,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,    12,    13,
      14,    15,    16,    17,    18,    19,    20,    21,    22,    23,
      24,    25,    26,     0,     0,     0,     0,     0,    31,    32,
      33,    34,    35,    36,     0,     0,     0,    40,    41,     0,
      42,    43,    44,    45,    46,    47,    48,    49,    50,    51,
      52,    53,    54,    55,     0,    56,    57,    58,     0,     0,
      60,     8,     9,    10,    11,     0,     0,     0,     0,     0,
       0,     0,     0,     0,     0,     0,    12,    13,    14,    15,
      16,    17,    18,    19,    20,    21,    22,    23,    24,    25,
      26,     0,     0,     0,     0,     0,    31,    32,    33,    34,
      35,    36,     0,     0,     0,    40,    41,     0,    42,    43,
      44,    45,    46,    47,    48,    49,    50,    51,    52,    53,
      54,    55,     0,    56,    57,    58,     0,   320,    60,     8,
       9,    10,    11,   321,     0,     0,     0,     0,     0,     0,
       0,     0,     0,     0,    12,    13,    14,    15,    16,    17,
      18,    19,    20,    21,    22,    23,    24,    25,    26,     0,
       0,     0,     0,     0,    31,    32,    33,    34,    35,    36,
       0,     0,     0,    40,    41,     0,    42,    43,    44,    45,
      46,    47,    48,    49,    50,    51,    52,    53,    54,    55,
       0,    56,    57,    58,     0,     0,    60
};

static const yytype_int16 yycheck[] =
{
       0,    40,    94,   135,   150,   115,     0,   111,   115,   134,
     140,   120,     0,    70,    76,   375,   120,   353,     9,   116,
     213,   118,   104,    92,   134,     9,     1,   134,     1,    68,
      76,    77,   164,    87,    88,   106,   140,   347,   374,   224,
     106,   148,   149,   114,   404,   102,   114,   113,   194,    40,
      41,    42,   114,   183,   111,   105,    40,    41,   127,    43,
     167,   111,   108,   120,   105,   104,   111,    51,   122,   123,
      70,   196,     9,    73,   131,   184,   105,   108,    78,   183,
     184,   138,   111,   140,    78,    85,   196,   397,   106,   196,
      78,   130,   224,   403,   240,   113,    69,   106,   223,    76,
     105,    74,   102,    40,    41,    42,   111,    85,    86,   105,
      83,   111,   244,   223,   105,   111,   223,   104,   111,   108,
     120,   114,   268,   105,   309,   125,   183,   184,   106,   111,
     113,   131,   110,   279,     4,     5,     6,   283,   138,   108,
     140,   271,   249,   250,   251,   252,   253,   254,   255,   256,
     257,   258,   259,   260,   261,   262,   263,   264,   265,   266,
     267,   354,   137,   111,   137,   105,   114,   271,   300,   301,
     114,   111,   111,   358,   104,   114,   361,   309,   111,   111,
     305,   114,   114,   183,   184,    40,    41,   312,     9,   256,
     257,   258,   259,    76,    77,   305,    76,    77,   305,   384,
      89,    90,   312,    79,    80,   312,   105,   353,    40,    41,
      42,    83,    84,   213,   271,   119,   120,   121,   104,    40,
      41,   406,    43,    44,   111,   112,   358,   125,   374,   361,
      51,    52,    53,     9,   254,   255,   368,   252,   253,   124,
     365,   260,   261,   389,   126,    91,    93,   107,   114,   353,
     114,   104,   384,   104,   346,   365,   402,   114,   365,   104,
     112,   104,   104,   113,    40,    41,   107,    43,    44,   109,
     374,   271,   106,   106,   406,    51,    52,    53,    94,    95,
      96,    97,    98,    99,   100,   101,   102,   103,   288,   108,
     105,   113,   112,   107,   107,   113,   104,   113,    56,   111,
      76,   107,   113,   107,   104,   114,   105,   109,   105,   107,
      17,   114,   108,   114,   241,   262,   265,   263,   113,   264,
     103,   131,   379,   266,    96,   267,   131,     5,   187,   229,
     395,   348,   313,   403,   288,   348,   374,    78,    -1,   378,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,   347,   348,    -1,
      -1,    -1,    -1,   353,   354,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,   374,    -1,    -1,    -1,    -1,   379,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,   397,    -1,    -1,
      -1,    -1,    -1,   403,     3,     4,     5,     6,     7,     8,
       9,    10,    11,    12,    13,    14,    15,    16,    -1,    18,
      19,    20,    21,    22,    23,    24,    25,    26,    27,    28,
      29,    30,    31,    32,    33,    34,    35,    36,    37,    38,
      39,    40,    41,    -1,    43,    44,    45,    46,    47,    48,
      49,    50,    51,    52,    53,    54,    55,    56,    57,    58,
      59,    60,    61,    62,    63,    64,    65,    66,    67,    68,
      69,    70,    -1,    72,    73,    74,    75,    76,    77,    78,
      79,    80,    81,    82,    -1,    -1,    85,    86,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,   104,    -1,    -1,    -1,   108,
     109,    -1,    -1,    -1,    -1,   114,   115,   116,   117,   118,
       3,     4,     5,     6,     7,     8,     9,    10,    11,    12,
      13,    14,    15,    16,    -1,    18,    19,    20,    21,    22,
      23,    24,    25,    26,    27,    28,    29,    30,    31,    32,
      33,    34,    35,    36,    37,    38,    39,    40,    41,    -1,
      43,    44,    45,    46,    47,    48,    49,    50,    51,    52,
      53,    54,    55,    56,    57,    58,    59,    60,    61,    62,
      63,    64,    65,    66,    67,    68,    69,    70,    -1,    72,
      73,    74,    75,    76,    77,    78,    79,    80,    81,    82,
      -1,    -1,    85,    86,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,   104,    -1,    -1,    -1,   108,   109,    -1,    -1,    -1,
      -1,   114,   115,   116,   117,   118,     3,     4,     5,     6,
       7,     8,     9,    10,    11,    12,    13,    14,    15,    16,
      -1,    18,    19,    20,    21,    22,    23,    24,    25,    26,
      27,    28,    29,    30,    31,    32,    33,    34,    35,    36,
      37,    38,    39,    40,    41,    -1,    43,    44,    45,    46,
      47,    48,    49,    50,    51,    52,    53,    54,    55,    56,
      57,    58,    59,    60,    61,    62,    63,    64,    65,    66,
      67,    68,    69,    70,    -1,    72,    73,    74,    75,    76,
      77,    78,    79,    80,    81,    82,    -1,    -1,    85,    86,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,   104,    -1,    -1,
      -1,   108,    -1,    -1,    -1,    -1,    -1,   114,   115,   116,
     117,   118,     3,     4,     5,     6,     7,     8,     9,    10,
      11,    12,    13,    14,    15,    16,    -1,    18,    19,    20,
      21,    22,    23,    24,    25,    26,    27,    28,    29,    30,
      31,    32,    33,    34,    35,    36,    37,    38,    39,    40,
      41,    -1,    43,    44,    45,    46,    47,    48,    49,    50,
      51,    52,    53,    54,    55,    56,    57,    58,    59,    60,
      61,    62,    63,    64,    65,    66,    67,    68,    69,    70,
      -1,    72,    73,    74,    75,    76,    77,    78,    79,    80,
      81,    82,    -1,    -1,    85,    86,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,   104,    -1,    -1,    -1,   108,    -1,    -1,
      -1,    -1,    -1,   114,   115,   116,   117,   118,     3,     4,
       5,     6,     7,     8,     9,    10,    11,    12,    13,    14,
      15,    16,    -1,    18,    19,    20,    21,    22,    23,    24,
      25,    26,    27,    28,    29,    30,    31,    32,    33,    34,
      35,    36,    37,    38,    39,    40,    41,    -1,    43,    44,
      45,    46,    47,    48,    49,    50,    51,    52,    53,    54,
      55,    56,    57,    58,    59,    60,    61,    62,    63,    64,
      65,    66,    67,    68,    69,    70,    -1,    72,    73,    74,
      75,    76,    77,    78,    79,    80,    81,    82,    -1,    -1,
      85,    86,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,   104,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,   114,
     115,   116,   117,   118,     3,     4,     5,     6,     7,     8,
       9,    10,    11,    12,    13,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    25,    26,    27,    28,
      29,    30,    31,    32,    33,    34,    35,    36,    37,    38,
      39,    40,    41,    -1,    43,    44,    45,    46,    47,    48,
      49,    50,    51,    52,    53,    54,    55,    -1,    57,    58,
      59,    60,    61,    62,    63,    64,    65,    66,    67,    68,
      69,    70,    -1,    72,    73,    74,    75,    76,    77,    78,
      79,    80,    81,    82,    -1,    -1,    85,    86,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,     3,     4,     5,     6,   104,     8,     9,    10,    11,
      12,    13,    -1,    -1,    -1,   114,   115,   116,   117,   118,
      -1,    -1,    -1,    25,    26,    27,    28,    29,    30,    31,
      32,    33,    34,    35,    36,    37,    38,    39,    40,    41,
      -1,    43,    44,    45,    46,    47,    48,    49,    50,    51,
      52,    53,    54,    55,    -1,    57,    58,    59,    60,    61,
      62,    63,    64,    65,    66,    67,    68,    69,    70,    -1,
      72,    73,    74,    75,    76,    77,    78,    79,    80,    81,
      82,    -1,    -1,    85,    86,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,     4,     5,     6,
      -1,    -1,   104,    10,    11,    12,    13,    -1,    -1,    -1,
      -1,    -1,    -1,   115,   116,   117,   118,    -1,    25,    26,
      27,    28,    29,    30,    31,    32,    33,    34,    35,    36,
      37,    38,    39,    -1,    -1,    -1,    -1,    -1,    45,    46,
      47,    48,    49,    50,    -1,    -1,    -1,    54,    55,    -1,
      57,    58,    59,    60,    61,    62,    63,    64,    65,    66,
      67,    68,    69,    70,    -1,    72,    73,    74,    -1,    76,
      77,    -1,    -1,    10,    11,    12,    13,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    25,    26,
      27,    28,    29,    30,    31,    32,    33,    34,    35,    36,
      37,    38,    39,    -1,    -1,    -1,    -1,   114,    45,    46,
      47,    48,    49,    50,    -1,    -1,    -1,    54,    55,    -1,
      57,    58,    59,    60,    61,    62,    63,    64,    65,    66,
      67,    68,    69,    70,    -1,    72,    73,    74,    -1,    76,
      77,    78,    79,    80,    81,    82,    -1,    -1,    85,    86,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,   104,    -1,    -1,
     107,    10,    11,    12,    13,    -1,    -1,    -1,   115,   116,
     117,   118,    -1,    -1,    -1,    -1,    25,    26,    27,    28,
      29,    30,    31,    32,    33,    34,    35,    36,    37,    38,
      39,    -1,    -1,    -1,    -1,    -1,    45,    46,    47,    48,
      49,    50,    -1,    -1,    -1,    54,    55,    -1,    57,    58,
      59,    60,    61,    62,    63,    64,    65,    66,    67,    68,
      69,    70,    -1,    72,    73,    74,    -1,    76,    77,    78,
      79,    80,    81,    82,    -1,    -1,    85,    86,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,   104,    -1,    -1,   107,    10,
      11,    12,    13,    -1,    -1,    -1,   115,   116,   117,   118,
      -1,    -1,    -1,    -1,    25,    26,    27,    28,    29,    30,
      31,    32,    33,    34,    35,    36,    37,    38,    39,    -1,
      -1,    -1,    -1,    -1,    45,    46,    47,    48,    49,    50,
      -1,    -1,    -1,    54,    55,    -1,    57,    58,    59,    60,
      61,    62,    63,    64,    65,    66,    67,    68,    69,    70,
      -1,    72,    73,    74,    -1,    76,    77,    78,    79,    80,
      81,    82,    -1,    -1,    85,    86,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,   104,    10,    11,    12,    13,    -1,    -1,
      -1,    -1,    -1,   114,   115,   116,   117,   118,    -1,    25,
      26,    27,    28,    29,    30,    31,    32,    33,    34,    35,
      36,    37,    38,    39,    -1,    -1,    -1,    -1,    -1,    45,
      46,    47,    48,    49,    50,    -1,    -1,    -1,    54,    55,
      -1,    57,    58,    59,    60,    61,    62,    63,    64,    65,
      66,    67,    68,    69,    70,    -1,    72,    73,    74,    -1,
      76,    77,    78,    79,    80,    81,    82,    -1,    -1,    85,
      86,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,   104,    -1,
      -1,   107,    10,    11,    12,    13,    -1,    -1,    -1,   115,
     116,   117,   118,    -1,    -1,    -1,    -1,    25,    26,    27,
      28,    29,    30,    31,    32,    33,    34,    35,    36,    37,
      38,    39,    -1,    -1,    -1,    -1,    -1,    45,    46,    47,
      48,    49,    50,    -1,    -1,    -1,    54,    55,    -1,    57,
      58,    59,    60,    61,    62,    63,    64,    65,    66,    67,
      68,    69,    70,    -1,    72,    73,    74,    -1,    76,    77,
      78,    79,    80,    81,    82,    -1,    -1,    85,    86,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,   104,    10,    11,    12,
      13,    -1,    -1,    -1,    -1,    -1,    -1,   115,   116,   117,
     118,    -1,    25,    26,    27,    28,    29,    30,    31,    32,
      33,    34,    35,    36,    37,    38,    39,    -1,    -1,    -1,
      -1,    -1,    45,    46,    47,    48,    49,    50,    -1,    -1,
      -1,    54,    55,    -1,    57,    58,    59,    60,    61,    62,
      63,    64,    65,    66,    67,    68,    69,    70,    -1,    72,
      73,    74,    -1,    76,    77,    78,    79,    80,    81,    82,
      -1,    -1,    85,    86,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,     3,     4,     5,
       6,   104,     8,     9,    10,    11,    12,    13,    -1,    -1,
      -1,    -1,   115,   116,   117,   118,    -1,    -1,    -1,    25,
      26,    27,    28,    29,    30,    31,    32,    33,    34,    35,
      36,    37,    38,    39,    40,    41,    -1,    43,    44,    45,
      46,    47,    48,    49,    50,    51,    52,    53,    54,    55,
      -1,    57,    58,    59,    60,    61,    62,    63,    64,    65,
      66,    67,    68,    69,    70,    -1,    72,    73,    74,    75,
      -1,    77,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,     3,     4,     5,     6,    -1,     8,     9,    10,
      11,    12,    13,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,   109,    25,    26,    27,    28,    29,    30,
      31,    32,    33,    34,    35,    36,    37,    38,    39,    40,
      41,    -1,    43,    44,    45,    46,    47,    48,    49,    50,
      51,    52,    53,    54,    55,    -1,    57,    58,    59,    60,
      61,    62,    63,    64,    65,    66,    67,    68,    69,    70,
      -1,    72,    73,    74,    75,    -1,    77,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,     3,     4,     5,
       6,    -1,     8,     9,    10,    11,    12,    13,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,   109,    25,
      26,    27,    28,    29,    30,    31,    32,    33,    34,    35,
      36,    37,    38,    39,    40,    41,    -1,    43,    44,    45,
      46,    47,    48,    49,    50,    51,    52,    53,    54,    55,
      -1,    57,    58,    59,    60,    61,    62,    63,    64,    65,
      66,    67,    68,    69,    70,    -1,    72,    73,    74,    75,
      -1,    77,    -1,    -1,    -1,    -1,    -1,    -1,    -1,     0,
      -1,    -1,     3,     4,     5,     6,     7,     8,     9,    10,
      11,    12,    13,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,   109,    25,    26,    27,    28,    29,    30,
      31,    32,    33,    34,    35,    36,    37,    38,    39,    40,
      41,    -1,    43,    44,    45,    46,    47,    48,    49,    50,
      51,    52,    53,    54,    55,    -1,    57,    58,    59,    60,
      61,    62,    63,    64,    65,    66,    67,    68,    69,    70,
      -1,    72,    73,    74,    75,    -1,    77,     3,     4,     5,
       6,     7,     8,     9,    10,    11,    12,    13,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    25,
      26,    27,    28,    29,    30,    31,    32,    33,    34,    35,
      36,    37,    38,    39,    40,    41,    -1,    43,    44,    45,
      46,    47,    48,    49,    50,    51,    52,    53,    54,    55,
      -1,    57,    58,    59,    60,    61,    62,    63,    64,    65,
      66,    67,    68,    69,    70,    -1,    72,    73,    74,    75,
      -1,    77,     3,     4,     5,     6,    -1,     8,     9,    10,
      11,    12,    13,    -1,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    25,    26,    27,    28,    29,    30,
      31,    32,    33,    34,    35,    36,    37,    38,    39,    40,
      41,    -1,    43,    44,    45,    46,    47,    48,    49,    50,
      51,    52,    53,    54,    55,    -1,    57,    58,    59,    60,
      61,    62,    63,    64,    65,    66,    67,    68,    69,    70,
      -1,    72,    73,    74,    75,    -1,    77,     4,     5,     6,
      -1,    -1,    -1,    10,    11,    12,    13,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    25,    26,
      27,    28,    29,    30,    31,    32,    33,    34,    35,    36,
      37,    38,    39,    -1,    -1,    -1,    -1,    -1,    45,    46,
      47,    48,    49,    50,    -1,    -1,    -1,    54,    55,    -1,
      57,    58,    59,    60,    61,    62,    63,    64,    65,    66,
      67,    68,    69,    70,    -1,    72,    73,    74,    -1,    -1,
      77,    10,    11,    12,    13,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    25,    26,    27,    28,
      29,    30,    31,    32,    33,    34,    35,    36,    37,    38,
      39,    -1,    -1,    -1,    -1,    -1,    45,    46,    47,    48,
      49,    50,    -1,    -1,    -1,    54,    55,    -1,    57,    58,
      59,    60,    61,    62,    63,    64,    65,    66,    67,    68,
      69,    70,    -1,    72,    73,    74,    -1,    76,    77,    10,
      11,    12,    13,    82,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    25,    26,    27,    28,    29,    30,
      31,    32,    33,    34,    35,    36,    37,    38,    39,    -1,
      -1,    -1,    -1,    -1,    45,    46,    47,    48,    49,    50,
      -1,    -1,    -1,    54,    55,    -1,    57,    58,    59,    60,
      61,    62,    63,    64,    65,    66,    67,    68,    69,    70,
      -1,    72,    73,    74,    -1,    -1,    77
};

  /* YYSTOS[STATE-NUM] -- The (internal number of the) accessing
     symbol of state STATE-NUM.  */
static const yytype_uint8 yystos[] =
{
       0,     3,     4,     5,     6,     7,     8,     9,    10,    11,
      12,    13,    25,    26,    27,    28,    29,    30,    31,    32,
      33,    34,    35,    36,    37,    38,    39,    40,    41,    43,
      44,    45,    46,    47,    48,    49,    50,    51,    52,    53,
      54,    55,    57,    58,    59,    60,    61,    62,    63,    64,
      65,    66,    67,    68,    69,    70,    72,    73,    74,    75,
      77,   160,   161,   162,   163,   164,   169,   170,   171,   172,
     174,   175,   176,   177,   178,   181,   182,   183,   218,   219,
     220,    44,    76,   172,   175,   177,    40,    41,    76,    77,
     108,   129,   104,   114,   221,   105,   111,     9,    40,    41,
      42,   166,   167,   173,   111,   114,    76,   129,   175,    76,
     114,   159,   176,   181,   175,   106,     0,   219,   175,   181,
     185,   108,    76,   179,   180,   108,   201,   166,   165,   168,
     176,   167,   129,   104,   106,   113,   108,     3,   174,   176,
     186,   187,    76,    78,    79,    80,    81,    82,    85,    86,
     104,   107,   115,   116,   117,   118,   130,   131,   132,   134,
     135,   136,   137,   138,   139,   140,   141,   142,   143,   144,
     145,   146,   147,   148,   149,   150,   151,   152,   153,   154,
     158,   181,   114,   186,   184,   113,   105,   111,    14,    15,
      16,    18,    19,    20,    21,    22,    23,    24,    56,   108,
     109,   114,   141,   154,   155,   157,   160,   161,   181,   191,
     192,   193,   194,   202,   203,   204,   206,   208,   210,   217,
     129,   165,   168,   106,   113,   107,   158,   155,   190,   176,
     129,   188,   189,   109,   187,   141,   141,   157,    85,    86,
     106,   110,   105,   105,   111,    55,   155,   104,   141,   119,
     120,   121,   116,   118,    83,    84,    87,    88,   122,   123,
      89,    90,   126,   125,   124,    91,    93,    92,   127,   107,
     109,   186,    79,    80,   180,   114,   114,   212,   104,   104,
     114,   114,   157,   104,   158,   112,   104,   109,   195,    94,
      95,    96,    97,    98,    99,   100,   101,   102,   103,   113,
     156,   111,   114,   109,   192,   106,   107,   158,   190,   113,
     107,   188,   106,   111,   114,    76,   114,   105,   133,   157,
      76,    82,   136,   155,   141,   141,   141,   143,   143,   144,
     144,   145,   145,   145,   145,   146,   146,   147,   148,   149,
     150,   151,   152,   157,   109,   198,   199,   200,   213,   157,
     114,   157,   112,   211,   202,   155,   155,   158,   113,   107,
     190,   113,   114,   158,   189,   106,   114,   107,   112,    56,
     201,   193,   191,   203,   214,   105,   105,   157,   171,   174,
     209,   196,   107,   190,   113,   190,   107,   158,   155,   104,
     209,   215,   216,   198,   205,   207,   129,   105,   109,   190,
     107,   157,   114,   105,    17,   194,   113,   193,   197,   201,
     114,   105,   157,   197,   198,   190,   114
};

  /* YYR1[YYN] -- Symbol number of symbol that rule YYN derives.  */
static const yytype_uint8 yyr1[] =
{
       0,   128,   129,   129,   130,   131,   131,   131,   131,   131,
     131,   132,   132,   132,   132,   132,   132,   133,   134,   135,
     135,   136,   136,   137,   137,   138,   138,   139,   140,   140,
     140,   141,   141,   141,   141,   142,   142,   142,   142,   143,
     143,   143,   143,   144,   144,   144,   145,   145,   145,   146,
     146,   146,   146,   146,   147,   147,   147,   148,   148,   149,
     149,   150,   150,   151,   151,   152,   152,   153,   153,   154,
     154,   155,   155,   156,   156,   156,   156,   156,   156,   156,
     156,   156,   156,   156,   157,   157,   158,   159,   160,   160,
     160,   160,   160,   160,   160,   161,   162,   162,   163,   163,
     164,   165,   165,   166,   166,   166,   166,   167,   167,   167,
     167,   168,   169,   169,   169,   169,   169,   169,   170,   170,
     170,   170,   170,   170,   170,   171,   171,   172,   172,   173,
     174,   174,   174,   174,   174,   174,   174,   174,   174,   174,
     175,   175,   175,   175,   175,   175,   176,   176,   177,   177,
     177,   178,   179,   179,   180,   180,   180,   181,   181,   181,
     182,   182,   182,   182,   182,   182,   182,   182,   182,   182,
     182,   182,   182,   182,   182,   182,   182,   182,   182,   182,
     182,   182,   182,   182,   182,   182,   182,   182,   182,   182,
     182,   182,   182,   182,   182,   182,   182,   182,   182,   182,
     182,   182,   182,   182,   182,   184,   183,   185,   183,   186,
     186,   187,   187,   188,   188,   189,   189,   190,   191,   192,
     192,   193,   193,   193,   193,   193,   193,   193,   194,   195,
     196,   194,   197,   197,   199,   198,   200,   198,   201,   201,
     202,   202,   203,   203,   204,   205,   205,   207,   206,   208,
     208,   209,   209,   211,   210,   212,   210,   213,   210,   214,
     214,   215,   215,   216,   216,   217,   217,   217,   217,   217,
     218,   218,   219,   219,   221,   220
};

  /* YYR2[YYN] -- Number of symbols on the right hand side of rule YYN.  */
static const yytype_uint8 yyr2[] =
{
       0,     2,     1,     1,     1,     1,     1,     1,     1,     1,
       3,     1,     4,     1,     3,     2,     2,     1,     1,     1,
       3,     2,     2,     2,     1,     2,     3,     2,     1,     1,
       1,     1,     2,     2,     2,     1,     1,     1,     1,     1,
       3,     3,     3,     1,     3,     3,     1,     3,     3,     1,
       3,     3,     3,     3,     1,     3,     3,     1,     3,     1,
       3,     1,     3,     1,     3,     1,     3,     1,     3,     1,
       5,     1,     3,     1,     1,     1,     1,     1,     1,     1,
       1,     1,     1,     1,     1,     3,     1,     2,     2,     2,
       4,     5,     6,     9,     2,     2,     1,     1,     2,     3,
       3,     2,     5,     3,     2,     3,     2,     0,     1,     1,
       1,     1,     1,     3,     6,     7,     8,     5,     1,     2,
       5,     6,     7,     4,     2,     1,     2,     1,     1,     1,
       1,     1,     2,     1,     2,     1,     1,     2,     2,     3,
       1,     1,     1,     2,     2,     1,     1,     2,     1,     1,
       1,     4,     1,     3,     1,     3,     3,     1,     3,     4,
       1,     1,     1,     1,     1,     1,     1,     1,     1,     1,
       1,     1,     1,     1,     1,     1,     1,     1,     1,     1,
       1,     1,     1,     1,     1,     1,     1,     1,     1,     1,
       1,     1,     1,     1,     1,     1,     1,     1,     1,     1,
       1,     1,     1,     1,     1,     0,     6,     0,     5,     1,
       2,     3,     4,     1,     3,     1,     4,     1,     1,     1,
       1,     1,     1,     1,     1,     1,     1,     1,     2,     0,
       0,     5,     1,     1,     0,     2,     0,     2,     2,     3,
       1,     2,     1,     2,     5,     3,     1,     0,     6,     3,
       2,     1,     4,     0,     6,     0,     8,     0,     7,     1,
       1,     1,     0,     2,     3,     2,     2,     2,     3,     2,
       1,     2,     1,     1,     0,     3
};


#define yyerrok         (yyerrstatus = 0)
#define yyclearin       (yychar = YYEMPTY)
#define YYEMPTY         (-2)
#define YYEOF           0

#define YYACCEPT        goto yyacceptlab
#define YYABORT         goto yyabortlab
#define YYERROR         goto yyerrorlab


#define YYRECOVERING()  (!!yyerrstatus)

#define YYBACKUP(Token, Value)                                  \
do                                                              \
  if (yychar == YYEMPTY)                                        \
    {                                                           \
      yychar = (Token);                                         \
      yylval = (Value);                                         \
      YYPOPSTACK (yylen);                                       \
      yystate = *yyssp;                                         \
      goto yybackup;                                            \
    }                                                           \
  else                                                          \
    {                                                           \
      yyerror (&yylloc, context, scanner, YY_("syntax error: cannot back up")); \
      YYERROR;                                                  \
    }                                                           \
while (0)

/* Error token number */
#define YYTERROR        1
#define YYERRCODE       256


/* YYLLOC_DEFAULT -- Set CURRENT to span from RHS[1] to RHS[N].
   If N is 0, then set CURRENT to the empty location which ends
   the previous symbol: RHS[0] (always defined).  */

#ifndef YYLLOC_DEFAULT
# define YYLLOC_DEFAULT(Current, Rhs, N)                                \
    do                                                                  \
      if (N)                                                            \
        {                                                               \
          (Current).first_line   = YYRHSLOC (Rhs, 1).first_line;        \
          (Current).first_column = YYRHSLOC (Rhs, 1).first_column;      \
          (Current).last_line    = YYRHSLOC (Rhs, N).last_line;         \
          (Current).last_column  = YYRHSLOC (Rhs, N).last_column;       \
        }                                                               \
      else                                                              \
        {                                                               \
          (Current).first_line   = (Current).last_line   =              \
            YYRHSLOC (Rhs, 0).last_line;                                \
          (Current).first_column = (Current).last_column =              \
            YYRHSLOC (Rhs, 0).last_column;                              \
        }                                                               \
    while (0)
#endif

#define YYRHSLOC(Rhs, K) ((Rhs)[K])


/* Enable debugging if requested.  */
#if YYDEBUG

# ifndef YYFPRINTF
#  include <stdio.h> /* INFRINGES ON USER NAME SPACE */
#  define YYFPRINTF fprintf
# endif

# define YYDPRINTF(Args)                        \
do {                                            \
  if (yydebug)                                  \
    YYFPRINTF Args;                             \
} while (0)


/* YY_LOCATION_PRINT -- Print the location on the stream.
   This macro was not mandated originally: define only if we know
   we won't break user code: when these are the locations we know.  */

#ifndef YY_LOCATION_PRINT
# if defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL

/* Print *YYLOCP on YYO.  Private, do not rely on its existence. */

YY_ATTRIBUTE_UNUSED
static unsigned
yy_location_print_ (FILE *yyo, YYLTYPE const * const yylocp)
{
  unsigned res = 0;
  int end_col = 0 != yylocp->last_column ? yylocp->last_column - 1 : 0;
  if (0 <= yylocp->first_line)
    {
      res += YYFPRINTF (yyo, "%d", yylocp->first_line);
      if (0 <= yylocp->first_column)
        res += YYFPRINTF (yyo, ".%d", yylocp->first_column);
    }
  if (0 <= yylocp->last_line)
    {
      if (yylocp->first_line < yylocp->last_line)
        {
          res += YYFPRINTF (yyo, "-%d", yylocp->last_line);
          if (0 <= end_col)
            res += YYFPRINTF (yyo, ".%d", end_col);
        }
      else if (0 <= end_col && yylocp->first_column < end_col)
        res += YYFPRINTF (yyo, "-%d", end_col);
    }
  return res;
 }

#  define YY_LOCATION_PRINT(File, Loc)          \
  yy_location_print_ (File, &(Loc))

# else
#  define YY_LOCATION_PRINT(File, Loc) ((void) 0)
# endif
#endif


# define YY_SYMBOL_PRINT(Title, Type, Value, Location)                    \
do {                                                                      \
  if (yydebug)                                                            \
    {                                                                     \
      YYFPRINTF (stderr, "%s ", Title);                                   \
      yy_symbol_print (stderr,                                            \
                  Type, Value, Location, context, scanner); \
      YYFPRINTF (stderr, "\n");                                           \
    }                                                                     \
} while (0)


/*----------------------------------------.
| Print this symbol's value on YYOUTPUT.  |
`----------------------------------------*/

static void
yy_symbol_value_print (FILE *yyoutput, int yytype, YYSTYPE const * const yyvaluep, YYLTYPE const * const yylocationp, TParseContext* context, void *scanner)
{
  FILE *yyo = yyoutput;
  YYUSE (yyo);
  YYUSE (yylocationp);
  YYUSE (context);
  YYUSE (scanner);
  if (!yyvaluep)
    return;
# ifdef YYPRINT
  if (yytype < YYNTOKENS)
    YYPRINT (yyoutput, yytoknum[yytype], *yyvaluep);
# endif
  YYUSE (yytype);
}


/*--------------------------------.
| Print this symbol on YYOUTPUT.  |
`--------------------------------*/

static void
yy_symbol_print (FILE *yyoutput, int yytype, YYSTYPE const * const yyvaluep, YYLTYPE const * const yylocationp, TParseContext* context, void *scanner)
{
  YYFPRINTF (yyoutput, "%s %s (",
             yytype < YYNTOKENS ? "token" : "nterm", yytname[yytype]);

  YY_LOCATION_PRINT (yyoutput, *yylocationp);
  YYFPRINTF (yyoutput, ": ");
  yy_symbol_value_print (yyoutput, yytype, yyvaluep, yylocationp, context, scanner);
  YYFPRINTF (yyoutput, ")");
}

/*------------------------------------------------------------------.
| yy_stack_print -- Print the state stack from its BOTTOM up to its |
| TOP (included).                                                   |
`------------------------------------------------------------------*/

static void
yy_stack_print (yytype_int16 *yybottom, yytype_int16 *yytop)
{
  YYFPRINTF (stderr, "Stack now");
  for (; yybottom <= yytop; yybottom++)
    {
      int yybot = *yybottom;
      YYFPRINTF (stderr, " %d", yybot);
    }
  YYFPRINTF (stderr, "\n");
}

# define YY_STACK_PRINT(Bottom, Top)                            \
do {                                                            \
  if (yydebug)                                                  \
    yy_stack_print ((Bottom), (Top));                           \
} while (0)


/*------------------------------------------------.
| Report that the YYRULE is going to be reduced.  |
`------------------------------------------------*/

static void
yy_reduce_print (yytype_int16 *yyssp, YYSTYPE *yyvsp, YYLTYPE *yylsp, int yyrule, TParseContext* context, void *scanner)
{
  unsigned long int yylno = yyrline[yyrule];
  int yynrhs = yyr2[yyrule];
  int yyi;
  YYFPRINTF (stderr, "Reducing stack by rule %d (line %lu):\n",
             yyrule - 1, yylno);
  /* The symbols being reduced.  */
  for (yyi = 0; yyi < yynrhs; yyi++)
    {
      YYFPRINTF (stderr, "   $%d = ", yyi + 1);
      yy_symbol_print (stderr,
                       yystos[yyssp[yyi + 1 - yynrhs]],
                       &(yyvsp[(yyi + 1) - (yynrhs)])
                       , &(yylsp[(yyi + 1) - (yynrhs)])                       , context, scanner);
      YYFPRINTF (stderr, "\n");
    }
}

# define YY_REDUCE_PRINT(Rule)          \
do {                                    \
  if (yydebug)                          \
    yy_reduce_print (yyssp, yyvsp, yylsp, Rule, context, scanner); \
} while (0)

/* Nonzero means print parse trace.  It is left uninitialized so that
   multiple parsers can coexist.  */
int yydebug;
#else /* !YYDEBUG */
# define YYDPRINTF(Args)
# define YY_SYMBOL_PRINT(Title, Type, Value, Location)
# define YY_STACK_PRINT(Bottom, Top)
# define YY_REDUCE_PRINT(Rule)
#endif /* !YYDEBUG */


/* YYINITDEPTH -- initial size of the parser's stacks.  */
#ifndef YYINITDEPTH
# define YYINITDEPTH 200
#endif

/* YYMAXDEPTH -- maximum size the stacks can grow to (effective only
   if the built-in stack extension method is used).

   Do not make this value too large; the results are undefined if
   YYSTACK_ALLOC_MAXIMUM < YYSTACK_BYTES (YYMAXDEPTH)
   evaluated with infinite-precision integer arithmetic.  */

#ifndef YYMAXDEPTH
# define YYMAXDEPTH 10000
#endif


#if YYERROR_VERBOSE

# ifndef yystrlen
#  if defined __GLIBC__ && defined _STRING_H
#   define yystrlen strlen
#  else
/* Return the length of YYSTR.  */
static YYSIZE_T
yystrlen (const char *yystr)
{
  YYSIZE_T yylen;
  for (yylen = 0; yystr[yylen]; yylen++)
    continue;
  return yylen;
}
#  endif
# endif

# ifndef yystpcpy
#  if defined __GLIBC__ && defined _STRING_H && defined _GNU_SOURCE
#   define yystpcpy stpcpy
#  else
/* Copy YYSRC to YYDEST, returning the address of the terminating '\0' in
   YYDEST.  */
static char *
yystpcpy (char *yydest, const char *yysrc)
{
  char *yyd = yydest;
  const char *yys = yysrc;

  while ((*yyd++ = *yys++) != '\0')
    continue;

  return yyd - 1;
}
#  endif
# endif

# ifndef yytnamerr
/* Copy to YYRES the contents of YYSTR after stripping away unnecessary
   quotes and backslashes, so that it's suitable for yyerror.  The
   heuristic is that double-quoting is unnecessary unless the string
   contains an apostrophe, a comma, or backslash (other than
   backslash-backslash).  YYSTR is taken from yytname.  If YYRES is
   null, do not copy; instead, return the length of what the result
   would have been.  */
static YYSIZE_T
yytnamerr (char *yyres, const char *yystr)
{
  if (*yystr == '"')
    {
      YYSIZE_T yyn = 0;
      char const *yyp = yystr;

      for (;;)
        switch (*++yyp)
          {
          case '\'':
          case ',':
            goto do_not_strip_quotes;

          case '\\':
            if (*++yyp != '\\')
              goto do_not_strip_quotes;
            /* Fall through.  */
          default:
            if (yyres)
              yyres[yyn] = *yyp;
            yyn++;
            break;

          case '"':
            if (yyres)
              yyres[yyn] = '\0';
            return yyn;
          }
    do_not_strip_quotes: ;
    }

  if (! yyres)
    return yystrlen (yystr);

  return yystpcpy (yyres, yystr) - yyres;
}
# endif

/* Copy into *YYMSG, which is of size *YYMSG_ALLOC, an error message
   about the unexpected token YYTOKEN for the state stack whose top is
   YYSSP.

   Return 0 if *YYMSG was successfully written.  Return 1 if *YYMSG is
   not large enough to hold the message.  In that case, also set
   *YYMSG_ALLOC to the required number of bytes.  Return 2 if the
   required number of bytes is too large to store.  */
static int
yysyntax_error (YYSIZE_T *yymsg_alloc, char **yymsg,
                yytype_int16 *yyssp, int yytoken)
{
  YYSIZE_T yysize0 = yytnamerr (YY_NULLPTR, yytname[yytoken]);
  YYSIZE_T yysize = yysize0;
  enum { YYERROR_VERBOSE_ARGS_MAXIMUM = 5 };
  /* Internationalized format string. */
  const char *yyformat = YY_NULLPTR;
  /* Arguments of yyformat. */
  char const *yyarg[YYERROR_VERBOSE_ARGS_MAXIMUM];
  /* Number of reported tokens (one for the "unexpected", one per
     "expected"). */
  int yycount = 0;

  /* There are many possibilities here to consider:
     - If this state is a consistent state with a default action, then
       the only way this function was invoked is if the default action
       is an error action.  In that case, don't check for expected
       tokens because there are none.
     - The only way there can be no lookahead present (in yychar) is if
       this state is a consistent state with a default action.  Thus,
       detecting the absence of a lookahead is sufficient to determine
       that there is no unexpected or expected token to report.  In that
       case, just report a simple "syntax error".
     - Don't assume there isn't a lookahead just because this state is a
       consistent state with a default action.  There might have been a
       previous inconsistent state, consistent state with a non-default
       action, or user semantic action that manipulated yychar.
     - Of course, the expected token list depends on states to have
       correct lookahead information, and it depends on the parser not
       to perform extra reductions after fetching a lookahead from the
       scanner and before detecting a syntax error.  Thus, state merging
       (from LALR or IELR) and default reductions corrupt the expected
       token list.  However, the list is correct for canonical LR with
       one exception: it will still contain any token that will not be
       accepted due to an error action in a later state.
  */
  if (yytoken != YYEMPTY)
    {
      int yyn = yypact[*yyssp];
      yyarg[yycount++] = yytname[yytoken];
      if (!yypact_value_is_default (yyn))
        {
          /* Start YYX at -YYN if negative to avoid negative indexes in
             YYCHECK.  In other words, skip the first -YYN actions for
             this state because they are default actions.  */
          int yyxbegin = yyn < 0 ? -yyn : 0;
          /* Stay within bounds of both yycheck and yytname.  */
          int yychecklim = YYLAST - yyn + 1;
          int yyxend = yychecklim < YYNTOKENS ? yychecklim : YYNTOKENS;
          int yyx;

          for (yyx = yyxbegin; yyx < yyxend; ++yyx)
            if (yycheck[yyx + yyn] == yyx && yyx != YYTERROR
                && !yytable_value_is_error (yytable[yyx + yyn]))
              {
                if (yycount == YYERROR_VERBOSE_ARGS_MAXIMUM)
                  {
                    yycount = 1;
                    yysize = yysize0;
                    break;
                  }
                yyarg[yycount++] = yytname[yyx];
                {
                  YYSIZE_T yysize1 = yysize + yytnamerr (YY_NULLPTR, yytname[yyx]);
                  if (! (yysize <= yysize1
                         && yysize1 <= YYSTACK_ALLOC_MAXIMUM))
                    return 2;
                  yysize = yysize1;
                }
              }
        }
    }

  switch (yycount)
    {
# define YYCASE_(N, S)                      \
      case N:                               \
        yyformat = S;                       \
      break
      YYCASE_(0, YY_("syntax error"));
      YYCASE_(1, YY_("syntax error, unexpected %s"));
      YYCASE_(2, YY_("syntax error, unexpected %s, expecting %s"));
      YYCASE_(3, YY_("syntax error, unexpected %s, expecting %s or %s"));
      YYCASE_(4, YY_("syntax error, unexpected %s, expecting %s or %s or %s"));
      YYCASE_(5, YY_("syntax error, unexpected %s, expecting %s or %s or %s or %s"));
# undef YYCASE_
    }

  {
    YYSIZE_T yysize1 = yysize + yystrlen (yyformat);
    if (! (yysize <= yysize1 && yysize1 <= YYSTACK_ALLOC_MAXIMUM))
      return 2;
    yysize = yysize1;
  }

  if (*yymsg_alloc < yysize)
    {
      *yymsg_alloc = 2 * yysize;
      if (! (yysize <= *yymsg_alloc
             && *yymsg_alloc <= YYSTACK_ALLOC_MAXIMUM))
        *yymsg_alloc = YYSTACK_ALLOC_MAXIMUM;
      return 1;
    }

  /* Avoid sprintf, as that infringes on the user's name space.
     Don't have undefined behavior even if the translation
     produced a string with the wrong number of "%s"s.  */
  {
    char *yyp = *yymsg;
    int yyi = 0;
    while ((*yyp = *yyformat) != '\0')
      if (*yyp == '%' && yyformat[1] == 's' && yyi < yycount)
        {
          yyp += yytnamerr (yyp, yyarg[yyi++]);
          yyformat += 2;
        }
      else
        {
          yyp++;
          yyformat++;
        }
  }
  return 0;
}
#endif /* YYERROR_VERBOSE */

/*-----------------------------------------------.
| Release the memory associated to this symbol.  |
`-----------------------------------------------*/

static void
yydestruct (const char *yymsg, int yytype, YYSTYPE *yyvaluep, YYLTYPE *yylocationp, TParseContext* context, void *scanner)
{
  YYUSE (yyvaluep);
  YYUSE (yylocationp);
  YYUSE (context);
  YYUSE (scanner);
  if (!yymsg)
    yymsg = "Deleting";
  YY_SYMBOL_PRINT (yymsg, yytype, yyvaluep, yylocationp);

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  YYUSE (yytype);
  YY_IGNORE_MAYBE_UNINITIALIZED_END
}




/*----------.
| yyparse.  |
`----------*/

int
yyparse (TParseContext* context, void *scanner)
{
/* The lookahead symbol.  */
int yychar;


/* The semantic value of the lookahead symbol.  */
/* Default value used for initialization, for pacifying older GCCs
   or non-GCC compilers.  */
YY_INITIAL_VALUE (static YYSTYPE yyval_default;)
YYSTYPE yylval YY_INITIAL_VALUE (= yyval_default);

/* Location data for the lookahead symbol.  */
static YYLTYPE yyloc_default
# if defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL
  = { 1, 1, 1, 1 }
# endif
;
YYLTYPE yylloc = yyloc_default;

    /* Number of syntax errors so far.  */
    int yynerrs;

    int yystate;
    /* Number of tokens to shift before error messages enabled.  */
    int yyerrstatus;

    /* The stacks and their tools:
       'yyss': related to states.
       'yyvs': related to semantic values.
       'yyls': related to locations.

       Refer to the stacks through separate pointers, to allow yyoverflow
       to reallocate them elsewhere.  */

    /* The state stack.  */
    yytype_int16 yyssa[YYINITDEPTH];
    yytype_int16 *yyss;
    yytype_int16 *yyssp;

    /* The semantic value stack.  */
    YYSTYPE yyvsa[YYINITDEPTH];
    YYSTYPE *yyvs;
    YYSTYPE *yyvsp;

    /* The location stack.  */
    YYLTYPE yylsa[YYINITDEPTH];
    YYLTYPE *yyls;
    YYLTYPE *yylsp;

    /* The locations where the error started and ended.  */
    YYLTYPE yyerror_range[3];

    YYSIZE_T yystacksize;

  int yyn;
  int yyresult;
  /* Lookahead token as an internal (translated) token number.  */
  int yytoken = 0;
  /* The variables used to return semantic value and location from the
     action routines.  */
  YYSTYPE yyval;
  YYLTYPE yyloc;

#if YYERROR_VERBOSE
  /* Buffer for error messages, and its allocated size.  */
  char yymsgbuf[128];
  char *yymsg = yymsgbuf;
  YYSIZE_T yymsg_alloc = sizeof yymsgbuf;
#endif

#define YYPOPSTACK(N)   (yyvsp -= (N), yyssp -= (N), yylsp -= (N))

  /* The number of symbols on the RHS of the reduced rule.
     Keep to zero when no symbol should be popped.  */
  int yylen = 0;

  yyssp = yyss = yyssa;
  yyvsp = yyvs = yyvsa;
  yylsp = yyls = yylsa;
  yystacksize = YYINITDEPTH;

  YYDPRINTF ((stderr, "Starting parse\n"));

  yystate = 0;
  yyerrstatus = 0;
  yynerrs = 0;
  yychar = YYEMPTY; /* Cause a token to be read.  */
  yylsp[0] = yylloc;
  goto yysetstate;

/*------------------------------------------------------------.
| yynewstate -- Push a new state, which is found in yystate.  |
`------------------------------------------------------------*/
 yynewstate:
  /* In all cases, when you get here, the value and location stacks
     have just been pushed.  So pushing a state here evens the stacks.  */
  yyssp++;

 yysetstate:
  *yyssp = yystate;

  if (yyss + yystacksize - 1 <= yyssp)
    {
      /* Get the current used size of the three stacks, in elements.  */
      YYSIZE_T yysize = yyssp - yyss + 1;

#ifdef yyoverflow
      {
        /* Give user a chance to reallocate the stack.  Use copies of
           these so that the &'s don't force the real ones into
           memory.  */
        YYSTYPE *yyvs1 = yyvs;
        yytype_int16 *yyss1 = yyss;
        YYLTYPE *yyls1 = yyls;

        /* Each stack pointer address is followed by the size of the
           data in use in that stack, in bytes.  This used to be a
           conditional around just the two extra args, but that might
           be undefined if yyoverflow is a macro.  */
        yyoverflow (YY_("memory exhausted"),
                    &yyss1, yysize * sizeof (*yyssp),
                    &yyvs1, yysize * sizeof (*yyvsp),
                    &yyls1, yysize * sizeof (*yylsp),
                    &yystacksize);

        yyls = yyls1;
        yyss = yyss1;
        yyvs = yyvs1;
      }
#else /* no yyoverflow */
# ifndef YYSTACK_RELOCATE
      goto yyexhaustedlab;
# else
      /* Extend the stack our own way.  */
      if (YYMAXDEPTH <= yystacksize)
        goto yyexhaustedlab;
      yystacksize *= 2;
      if (YYMAXDEPTH < yystacksize)
        yystacksize = YYMAXDEPTH;

      {
        yytype_int16 *yyss1 = yyss;
        union yyalloc *yyptr =
          (union yyalloc *) YYSTACK_ALLOC (YYSTACK_BYTES (yystacksize));
        if (! yyptr)
          goto yyexhaustedlab;
        YYSTACK_RELOCATE (yyss_alloc, yyss);
        YYSTACK_RELOCATE (yyvs_alloc, yyvs);
        YYSTACK_RELOCATE (yyls_alloc, yyls);
#  undef YYSTACK_RELOCATE
        if (yyss1 != yyssa)
          YYSTACK_FREE (yyss1);
      }
# endif
#endif /* no yyoverflow */

      yyssp = yyss + yysize - 1;
      yyvsp = yyvs + yysize - 1;
      yylsp = yyls + yysize - 1;

      YYDPRINTF ((stderr, "Stack size increased to %lu\n",
                  (unsigned long int) yystacksize));

      if (yyss + yystacksize - 1 <= yyssp)
        YYABORT;
    }

  YYDPRINTF ((stderr, "Entering state %d\n", yystate));

  if (yystate == YYFINAL)
    YYACCEPT;

  goto yybackup;

/*-----------.
| yybackup.  |
`-----------*/
yybackup:

  /* Do appropriate processing given the current state.  Read a
     lookahead token if we need one and don't already have one.  */

  /* First try to decide what to do without reference to lookahead token.  */
  yyn = yypact[yystate];
  if (yypact_value_is_default (yyn))
    goto yydefault;

  /* Not known => get a lookahead token if don't already have one.  */

  /* YYCHAR is either YYEMPTY or YYEOF or a valid lookahead symbol.  */
  if (yychar == YYEMPTY)
    {
      YYDPRINTF ((stderr, "Reading a token: "));
      yychar = yylex (&yylval, &yylloc, scanner);
    }

  if (yychar <= YYEOF)
    {
      yychar = yytoken = YYEOF;
      YYDPRINTF ((stderr, "Now at end of input.\n"));
    }
  else
    {
      yytoken = YYTRANSLATE (yychar);
      YY_SYMBOL_PRINT ("Next token is", yytoken, &yylval, &yylloc);
    }

  /* If the proper action on seeing token YYTOKEN is to reduce or to
     detect an error, take that action.  */
  yyn += yytoken;
  if (yyn < 0 || YYLAST < yyn || yycheck[yyn] != yytoken)
    goto yydefault;
  yyn = yytable[yyn];
  if (yyn <= 0)
    {
      if (yytable_value_is_error (yyn))
        goto yyerrlab;
      yyn = -yyn;
      goto yyreduce;
    }

  /* Count tokens shifted since error; after three, turn off error
     status.  */
  if (yyerrstatus)
    yyerrstatus--;

  /* Shift the lookahead token.  */
  YY_SYMBOL_PRINT ("Shifting", yytoken, &yylval, &yylloc);

  /* Discard the shifted token.  */
  yychar = YYEMPTY;

  yystate = yyn;
  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END
  *++yylsp = yylloc;
  goto yynewstate;


/*-----------------------------------------------------------.
| yydefault -- do the default action for the current state.  |
`-----------------------------------------------------------*/
yydefault:
  yyn = yydefact[yystate];
  if (yyn == 0)
    goto yyerrlab;
  goto yyreduce;


/*-----------------------------.
| yyreduce -- Do a reduction.  |
`-----------------------------*/
yyreduce:
  /* yyn is the number of a rule to reduce with.  */
  yylen = yyr2[yyn];

  /* If YYLEN is nonzero, implement the default value of the action:
     '$$ = $1'.

     Otherwise, the following line sets YYVAL to garbage.
     This behavior is undocumented and Bison
     users should not rely upon it.  Assigning to YYVAL
     unconditionally makes the parser a bit smaller, and it avoids a
     GCC warning that YYVAL may be used uninitialized.  */
  yyval = yyvsp[1-yylen];

  /* Default location.  */
  YYLLOC_DEFAULT (yyloc, (yylsp - yylen), yylen);
  YY_REDUCE_PRINT (yyn);
  switch (yyn)
    {
        case 4:

    {
        // The symbol table search was done in the lexical phase
        (yyval.interm.intermTypedNode) = context->parseVariableIdentifier((yylsp[0]), (yyvsp[0].lex).string, (yyvsp[0].lex).symbol);

        // don't delete $1.string, it's used by error recovery, and the pool
        // pop will reclaim the memory
    }

    break;

  case 5:

    {
        (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode);
    }

    break;

  case 6:

    {
        TConstantUnion *unionArray = new TConstantUnion[1];
        unionArray->setIConst((yyvsp[0].lex).i);
        (yyval.interm.intermTypedNode) = context->intermediate.addConstantUnion(unionArray, TType(EbtInt, EbpUndefined, EvqConst), (yylsp[0]));
    }

    break;

  case 7:

    {
        TConstantUnion *unionArray = new TConstantUnion[1];
        unionArray->setUConst((yyvsp[0].lex).u);
        (yyval.interm.intermTypedNode) = context->intermediate.addConstantUnion(unionArray, TType(EbtUInt, EbpUndefined, EvqConst), (yylsp[0]));
    }

    break;

  case 8:

    {
        TConstantUnion *unionArray = new TConstantUnion[1];
        unionArray->setFConst((yyvsp[0].lex).f);
        (yyval.interm.intermTypedNode) = context->intermediate.addConstantUnion(unionArray, TType(EbtFloat, EbpUndefined, EvqConst), (yylsp[0]));
    }

    break;

  case 9:

    {
        TConstantUnion *unionArray = new TConstantUnion[1];
        unionArray->setBConst((yyvsp[0].lex).b);
        (yyval.interm.intermTypedNode) = context->intermediate.addConstantUnion(unionArray, TType(EbtBool, EbpUndefined, EvqConst), (yylsp[0]));
    }

    break;

  case 10:

    {
        (yyval.interm.intermTypedNode) = (yyvsp[-1].interm.intermTypedNode);
    }

    break;

  case 11:

    {
        (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode);
    }

    break;

  case 12:

    {
        (yyval.interm.intermTypedNode) = context->addIndexExpression((yyvsp[-3].interm.intermTypedNode), (yylsp[-2]), (yyvsp[-1].interm.intermTypedNode));
    }

    break;

  case 13:

    {
        (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode);
    }

    break;

  case 14:

    {
        (yyval.interm.intermTypedNode) = context->addFieldSelectionExpression((yyvsp[-2].interm.intermTypedNode), (yylsp[-1]), *(yyvsp[0].lex).string, (yylsp[0]));
    }

    break;

  case 15:

    {
        (yyval.interm.intermTypedNode) = context->addUnaryMathLValue(EOpPostIncrement, (yyvsp[-1].interm.intermTypedNode), (yylsp[0]));
    }

    break;

  case 16:

    {
        (yyval.interm.intermTypedNode) = context->addUnaryMathLValue(EOpPostDecrement, (yyvsp[-1].interm.intermTypedNode), (yylsp[0]));
    }

    break;

  case 17:

    {
        if (context->integerErrorCheck((yyvsp[0].interm.intermTypedNode), "[]"))
            context->recover();
        (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode);
    }

    break;

  case 18:

    {
        bool fatalError = false;
        (yyval.interm.intermTypedNode) = context->addFunctionCallOrMethod((yyvsp[0].interm).function, (yyvsp[0].interm).nodePair.node1, (yyvsp[0].interm).nodePair.node2, (yylsp[0]), &fatalError);
        if (fatalError)
        {
            YYERROR;
        }
    }

    break;

  case 19:

    {
        (yyval.interm) = (yyvsp[0].interm);
        (yyval.interm).nodePair.node2 = nullptr;
    }

    break;

  case 20:

    {
        ES3_ONLY("", (yylsp[0]), "methods");
        (yyval.interm) = (yyvsp[0].interm);
        (yyval.interm).nodePair.node2 = (yyvsp[-2].interm.intermTypedNode);
    }

    break;

  case 21:

    {
        (yyval.interm) = (yyvsp[-1].interm);
    }

    break;

  case 22:

    {
        (yyval.interm) = (yyvsp[-1].interm);
    }

    break;

  case 23:

    {
        (yyval.interm).function = (yyvsp[-1].interm.function);
        (yyval.interm).nodePair.node1 = nullptr;
    }

    break;

  case 24:

    {
        (yyval.interm).function = (yyvsp[0].interm.function);
        (yyval.interm).nodePair.node1 = nullptr;
    }

    break;

  case 25:

    {
        const TType *type = new TType((yyvsp[0].interm.intermTypedNode)->getType());
        (yyvsp[-1].interm.function)->addParameter(TConstParameter(type));
        (yyval.interm).function = (yyvsp[-1].interm.function);
        (yyval.interm).nodePair.node1 = context->intermediate.makeAggregate((yyvsp[0].interm.intermTypedNode), (yylsp[0]));
    }

    break;

  case 26:

    {
        const TType *type = new TType((yyvsp[0].interm.intermTypedNode)->getType());
        (yyvsp[-2].interm).function->addParameter(TConstParameter(type));
        (yyval.interm).function = (yyvsp[-2].interm).function;
        (yyval.interm).nodePair.node1 = context->intermediate.growAggregate((yyvsp[-2].interm).intermNode, (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 27:

    {
        (yyval.interm.function) = (yyvsp[-1].interm.function);
    }

    break;

  case 28:

    {
        if ((yyvsp[0].interm.type).array) {
            ES3_ONLY("[]", (yylsp[0]), "array constructor");
        }
        (yyval.interm.function) = context->addConstructorFunc((yyvsp[0].interm.type));
    }

    break;

  case 29:

    {
        if (context->reservedErrorCheck((yylsp[0]), *(yyvsp[0].lex).string))
            context->recover();
        const TType *type = TCache::getType(EbtVoid, EbpUndefined);
        TFunction *function = new TFunction((yyvsp[0].lex).string, type);
        (yyval.interm.function) = function;
    }

    break;

  case 30:

    {
        if (context->reservedErrorCheck((yylsp[0]), *(yyvsp[0].lex).string))
            context->recover();
        const TType *type = TCache::getType(EbtVoid, EbpUndefined);
        TFunction *function = new TFunction((yyvsp[0].lex).string, type);
        (yyval.interm.function) = function;
    }

    break;

  case 31:

    {
        (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode);
    }

    break;

  case 32:

    {
        (yyval.interm.intermTypedNode) = context->addUnaryMathLValue(EOpPreIncrement, (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 33:

    {
        (yyval.interm.intermTypedNode) = context->addUnaryMathLValue(EOpPreDecrement, (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 34:

    {
        if ((yyvsp[-1].interm).op != EOpNull) {
            (yyval.interm.intermTypedNode) = context->addUnaryMath((yyvsp[-1].interm).op, (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
        } else
            (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode);
    }

    break;

  case 35:

    { (yyval.interm).op = EOpPositive; }

    break;

  case 36:

    { (yyval.interm).op = EOpNegative; }

    break;

  case 37:

    { (yyval.interm).op = EOpLogicalNot; }

    break;

  case 38:

    {
        ES3_ONLY("~", (yyloc), "bit-wise operator");
        (yyval.interm).op = EOpBitwiseNot;
    }

    break;

  case 39:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 40:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMath(EOpMul, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 41:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMath(EOpDiv, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 42:

    {
        ES3_ONLY("%", (yylsp[-1]), "integer modulus operator");
        (yyval.interm.intermTypedNode) = context->addBinaryMath(EOpIMod, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 43:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 44:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMath(EOpAdd, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 45:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMath(EOpSub, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 46:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 47:

    {
        ES3_ONLY("<<", (yylsp[-1]), "bit-wise operator");
        (yyval.interm.intermTypedNode) = context->addBinaryMath(EOpBitShiftLeft, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 48:

    {
        ES3_ONLY(">>", (yylsp[-1]), "bit-wise operator");
        (yyval.interm.intermTypedNode) = context->addBinaryMath(EOpBitShiftRight, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 49:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 50:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMathBooleanResult(EOpLessThan, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 51:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMathBooleanResult(EOpGreaterThan, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 52:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMathBooleanResult(EOpLessThanEqual, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 53:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMathBooleanResult(EOpGreaterThanEqual, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 54:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 55:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMathBooleanResult(EOpEqual, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 56:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMathBooleanResult(EOpNotEqual, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 57:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 58:

    {
        ES3_ONLY("&", (yylsp[-1]), "bit-wise operator");
        (yyval.interm.intermTypedNode) = context->addBinaryMath(EOpBitwiseAnd, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 59:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 60:

    {
        ES3_ONLY("^", (yylsp[-1]), "bit-wise operator");
        (yyval.interm.intermTypedNode) = context->addBinaryMath(EOpBitwiseXor, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 61:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 62:

    {
        ES3_ONLY("|", (yylsp[-1]), "bit-wise operator");
        (yyval.interm.intermTypedNode) = context->addBinaryMath(EOpBitwiseOr, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 63:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 64:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMathBooleanResult(EOpLogicalAnd, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 65:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 66:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMathBooleanResult(EOpLogicalXor, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 67:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 68:

    {
        (yyval.interm.intermTypedNode) = context->addBinaryMathBooleanResult(EOpLogicalOr, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 69:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 70:

    {
        (yyval.interm.intermTypedNode) = context->addTernarySelection((yyvsp[-4].interm.intermTypedNode), (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-3]));
    }

    break;

  case 71:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 72:

    {
        if (context->lValueErrorCheck((yylsp[-1]), "assign", (yyvsp[-2].interm.intermTypedNode)))
            context->recover();
        (yyval.interm.intermTypedNode) = context->addAssign((yyvsp[-1].interm).op, (yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 73:

    { (yyval.interm).op = EOpAssign; }

    break;

  case 74:

    { (yyval.interm).op = EOpMulAssign; }

    break;

  case 75:

    { (yyval.interm).op = EOpDivAssign; }

    break;

  case 76:

    {
        ES3_ONLY("%=", (yyloc), "integer modulus operator");
        (yyval.interm).op = EOpIModAssign;
    }

    break;

  case 77:

    { (yyval.interm).op = EOpAddAssign; }

    break;

  case 78:

    { (yyval.interm).op = EOpSubAssign; }

    break;

  case 79:

    {
        ES3_ONLY("<<=", (yyloc), "bit-wise operator");
        (yyval.interm).op = EOpBitShiftLeftAssign;
    }

    break;

  case 80:

    {
        ES3_ONLY(">>=", (yyloc), "bit-wise operator");
        (yyval.interm).op = EOpBitShiftRightAssign;
    }

    break;

  case 81:

    {
        ES3_ONLY("&=", (yyloc), "bit-wise operator");
        (yyval.interm).op = EOpBitwiseAndAssign;
    }

    break;

  case 82:

    {
        ES3_ONLY("^=", (yyloc), "bit-wise operator");
        (yyval.interm).op = EOpBitwiseXorAssign;
    }

    break;

  case 83:

    {
        ES3_ONLY("|=", (yyloc), "bit-wise operator");
        (yyval.interm).op = EOpBitwiseOrAssign;
    }

    break;

  case 84:

    {
        (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode);
    }

    break;

  case 85:

    {
        (yyval.interm.intermTypedNode) = context->addComma((yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.intermTypedNode), (yylsp[-1]));
    }

    break;

  case 86:

    {
        if (context->constErrorCheck((yyvsp[0].interm.intermTypedNode)))
            context->recover();
        (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode);
    }

    break;

  case 87:

    {
        if (context->enterStructDeclaration((yylsp[-1]), *(yyvsp[-1].lex).string))
            context->recover();
        (yyval.lex) = (yyvsp[-1].lex);
    }

    break;

  case 88:

    {
        (yyval.interm.intermNode) = context->addFunctionPrototypeDeclaration(*((yyvsp[-1].interm).function), (yylsp[-1]));
    }

    break;

  case 89:

    {
        TIntermAggregate *aggNode = (yyvsp[-1].interm).intermAggregate;
        if (aggNode && aggNode->getOp() == EOpNull)
            aggNode->setOp(EOpDeclaration);
        (yyval.interm.intermNode) = aggNode;
    }

    break;

  case 90:

    {
        if (((yyvsp[-2].interm.precision) == EbpHigh) && (context->getShaderType() == GL_FRAGMENT_SHADER) && !context->getFragmentPrecisionHigh()) {
            context->error((yylsp[-3]), "precision is not supported in fragment shader", "highp");
            context->recover();
        }
        if (!context->symbolTable.setDefaultPrecision( (yyvsp[-1].interm.type), (yyvsp[-2].interm.precision) )) {
            context->error((yylsp[-3]), "illegal type argument for default precision qualifier", getBasicString((yyvsp[-1].interm.type).type));
            context->recover();
        }
        (yyval.interm.intermNode) = 0;
    }

    break;

  case 91:

    {
        ES3_ONLY(getQualifierString((yyvsp[-4].interm.type).qualifier), (yylsp[-4]), "interface blocks");
        (yyval.interm.intermNode) = context->addInterfaceBlock((yyvsp[-4].interm.type), (yylsp[-3]), *(yyvsp[-3].lex).string, (yyvsp[-2].interm.fieldList), NULL, (yyloc), NULL, (yyloc));
    }

    break;

  case 92:

    {
        ES3_ONLY(getQualifierString((yyvsp[-5].interm.type).qualifier), (yylsp[-5]), "interface blocks");
        (yyval.interm.intermNode) = context->addInterfaceBlock((yyvsp[-5].interm.type), (yylsp[-4]), *(yyvsp[-4].lex).string, (yyvsp[-3].interm.fieldList), (yyvsp[-1].lex).string, (yylsp[-1]), NULL, (yyloc));
    }

    break;

  case 93:

    {
        ES3_ONLY(getQualifierString((yyvsp[-8].interm.type).qualifier), (yylsp[-8]), "interface blocks");
        (yyval.interm.intermNode) = context->addInterfaceBlock((yyvsp[-8].interm.type), (yylsp[-7]), *(yyvsp[-7].lex).string, (yyvsp[-6].interm.fieldList), (yyvsp[-4].lex).string, (yylsp[-4]), (yyvsp[-2].interm.intermTypedNode), (yylsp[-3]));
    }

    break;

  case 94:

    {
        context->parseGlobalLayoutQualifier((yyvsp[-1].interm.type));
        (yyval.interm.intermNode) = 0;
    }

    break;

  case 95:

    {
        (yyval.interm).function = context->parseFunctionDeclarator((yylsp[0]), (yyvsp[-1].interm.function));
    }

    break;

  case 96:

    {
        (yyval.interm.function) = (yyvsp[0].interm.function);
    }

    break;

  case 97:

    {
        (yyval.interm.function) = (yyvsp[0].interm.function);
    }

    break;

  case 98:

    {
        // Add the parameter
        (yyval.interm.function) = (yyvsp[-1].interm.function);
        if ((yyvsp[0].interm).param.type->getBasicType() != EbtVoid)
            (yyvsp[-1].interm.function)->addParameter((yyvsp[0].interm).param.turnToConst());
        else
            delete (yyvsp[0].interm).param.type;
    }

    break;

  case 99:

    {
        //
        // Only first parameter of one-parameter functions can be void
        // The check for named parameters not being void is done in parameter_declarator
        //
        if ((yyvsp[0].interm).param.type->getBasicType() == EbtVoid) {
            //
            // This parameter > first is void
            //
            context->error((yylsp[-1]), "cannot be an argument type except for '(void)'", "void");
            context->recover();
            delete (yyvsp[0].interm).param.type;
        } else {
            // Add the parameter
            (yyval.interm.function) = (yyvsp[-2].interm.function);
            (yyvsp[-2].interm.function)->addParameter((yyvsp[0].interm).param.turnToConst());
        }
    }

    break;

  case 100:

    {
        if ((yyvsp[-2].interm.type).qualifier != EvqGlobal && (yyvsp[-2].interm.type).qualifier != EvqTemporary)
        {
            context->error((yylsp[-1]), "no qualifiers allowed for function return", getQualifierString((yyvsp[-2].interm.type).qualifier));
            context->recover();
        }
        if (!(yyvsp[-2].interm.type).layoutQualifier.isEmpty())
        {
            context->error((yylsp[-1]), "no qualifiers allowed for function return", "layout");
            context->recover();
        }
        // make sure a sampler is not involved as well...
        if (context->samplerErrorCheck((yylsp[-1]), (yyvsp[-2].interm.type), "samplers can't be function return values"))
            context->recover();

        // Add the function as a prototype after parsing it (we do not support recursion)
        TFunction *function;
        const TType *type = new TType((yyvsp[-2].interm.type));
        function = new TFunction((yyvsp[-1].lex).string, type);
        (yyval.interm.function) = function;
        
        context->symbolTable.push();
    }

    break;

  case 101:

    {
        if ((yyvsp[-1].interm.type).type == EbtVoid) {
            context->error((yylsp[0]), "illegal use of type 'void'", (yyvsp[0].lex).string->c_str());
            context->recover();
        }
        if (context->reservedErrorCheck((yylsp[0]), *(yyvsp[0].lex).string))
            context->recover();
        TParameter param = {(yyvsp[0].lex).string, new TType((yyvsp[-1].interm.type))};
        (yyval.interm).param = param;
    }

    break;

  case 102:

    {
        // Check that we can make an array out of this type
        if (context->arrayTypeErrorCheck((yylsp[-2]), (yyvsp[-4].interm.type)))
            context->recover();

        if (context->reservedErrorCheck((yylsp[-3]), *(yyvsp[-3].lex).string))
            context->recover();

        int size;
        if (context->arraySizeErrorCheck((yylsp[-2]), (yyvsp[-1].interm.intermTypedNode), size))
            context->recover();
        (yyvsp[-4].interm.type).setArraySize(size);

        TType* type = new TType((yyvsp[-4].interm.type));
        TParameter param = { (yyvsp[-3].lex).string, type };
        (yyval.interm).param = param;
    }

    break;

  case 103:

    {
        (yyval.interm) = (yyvsp[0].interm);
        if (context->paramErrorCheck((yylsp[0]), (yyvsp[-2].interm.qualifier), (yyvsp[-1].interm.qualifier), (yyval.interm).param.type))
            context->recover();
    }

    break;

  case 104:

    {
        (yyval.interm) = (yyvsp[0].interm);
        if (context->parameterSamplerErrorCheck((yylsp[0]), (yyvsp[-1].interm.qualifier), *(yyvsp[0].interm).param.type))
            context->recover();
        if (context->paramErrorCheck((yylsp[0]), EvqTemporary, (yyvsp[-1].interm.qualifier), (yyval.interm).param.type))
            context->recover();
    }

    break;

  case 105:

    {
        (yyval.interm) = (yyvsp[0].interm);
        if (context->paramErrorCheck((yylsp[0]), (yyvsp[-2].interm.qualifier), (yyvsp[-1].interm.qualifier), (yyval.interm).param.type))
            context->recover();
    }

    break;

  case 106:

    {
        (yyval.interm) = (yyvsp[0].interm);
        if (context->parameterSamplerErrorCheck((yylsp[0]), (yyvsp[-1].interm.qualifier), *(yyvsp[0].interm).param.type))
            context->recover();
        if (context->paramErrorCheck((yylsp[0]), EvqTemporary, (yyvsp[-1].interm.qualifier), (yyval.interm).param.type))
            context->recover();
    }

    break;

  case 107:

    {
        (yyval.interm.qualifier) = EvqIn;
    }

    break;

  case 108:

    {
        (yyval.interm.qualifier) = EvqIn;
    }

    break;

  case 109:

    {
        (yyval.interm.qualifier) = EvqOut;
    }

    break;

  case 110:

    {
        (yyval.interm.qualifier) = EvqInOut;
    }

    break;

  case 111:

    {
        TParameter param = { 0, new TType((yyvsp[0].interm.type)) };
        (yyval.interm).param = param;
    }

    break;

  case 112:

    {
        (yyval.interm) = (yyvsp[0].interm);
    }

    break;

  case 113:

    {
        (yyval.interm) = (yyvsp[-2].interm);
        (yyval.interm).intermAggregate = context->parseDeclarator((yyval.interm).type, (yyvsp[-2].interm).intermAggregate, (yylsp[0]), *(yyvsp[0].lex).string);
    }

    break;

  case 114:

    {
        (yyval.interm) = (yyvsp[-5].interm);
        (yyval.interm).intermAggregate = context->parseArrayDeclarator((yyval.interm).type, (yyvsp[-5].interm).intermAggregate, (yylsp[-3]), *(yyvsp[-3].lex).string, (yylsp[-2]), (yyvsp[-1].interm.intermTypedNode));
    }

    break;

  case 115:

    {
        ES3_ONLY("[]", (yylsp[-4]), "implicitly sized array");
        (yyval.interm) = (yyvsp[-6].interm);
        (yyval.interm).intermAggregate = context->parseArrayInitDeclarator((yyval.interm).type, (yyvsp[-6].interm).intermAggregate, (yylsp[-4]), *(yyvsp[-4].lex).string, (yylsp[-3]), nullptr, (yylsp[-1]), (yyvsp[0].interm.intermTypedNode));
    }

    break;

  case 116:

    {
        ES3_ONLY("=", (yylsp[-1]), "first-class arrays (array initializer)");
        (yyval.interm) = (yyvsp[-7].interm);
        (yyval.interm).intermAggregate = context->parseArrayInitDeclarator((yyval.interm).type, (yyvsp[-7].interm).intermAggregate, (yylsp[-5]), *(yyvsp[-5].lex).string, (yylsp[-4]), (yyvsp[-3].interm.intermTypedNode), (yylsp[-1]), (yyvsp[0].interm.intermTypedNode));
    }

    break;

  case 117:

    {
        (yyval.interm) = (yyvsp[-4].interm);
        (yyval.interm).intermAggregate = context->parseInitDeclarator((yyval.interm).type, (yyvsp[-4].interm).intermAggregate, (yylsp[-2]), *(yyvsp[-2].lex).string, (yylsp[-1]), (yyvsp[0].interm.intermTypedNode));
    }

    break;

  case 118:

    {
        (yyval.interm).type = (yyvsp[0].interm.type);
        (yyval.interm).intermAggregate = context->parseSingleDeclaration((yyval.interm).type, (yylsp[0]), "");
    }

    break;

  case 119:

    {
        (yyval.interm).type = (yyvsp[-1].interm.type);
        (yyval.interm).intermAggregate = context->parseSingleDeclaration((yyval.interm).type, (yylsp[0]), *(yyvsp[0].lex).string);
    }

    break;

  case 120:

    {
        (yyval.interm).type = (yyvsp[-4].interm.type);
        (yyval.interm).intermAggregate = context->parseSingleArrayDeclaration((yyval.interm).type, (yylsp[-3]), *(yyvsp[-3].lex).string, (yylsp[-2]), (yyvsp[-1].interm.intermTypedNode));
    }

    break;

  case 121:

    {
        ES3_ONLY("[]", (yylsp[-3]), "implicitly sized array");
        (yyval.interm).type = (yyvsp[-5].interm.type);
        (yyval.interm).intermAggregate = context->parseSingleArrayInitDeclaration((yyval.interm).type, (yylsp[-4]), *(yyvsp[-4].lex).string, (yylsp[-3]), nullptr, (yylsp[-1]), (yyvsp[0].interm.intermTypedNode));
    }

    break;

  case 122:

    {
        ES3_ONLY("=", (yylsp[-1]), "first-class arrays (array initializer)");
        (yyval.interm).type = (yyvsp[-6].interm.type);
        (yyval.interm).intermAggregate = context->parseSingleArrayInitDeclaration((yyval.interm).type, (yylsp[-5]), *(yyvsp[-5].lex).string, (yylsp[-4]), (yyvsp[-3].interm.intermTypedNode), (yylsp[-1]), (yyvsp[0].interm.intermTypedNode));
    }

    break;

  case 123:

    {
        (yyval.interm).type = (yyvsp[-3].interm.type);
        (yyval.interm).intermAggregate = context->parseSingleInitDeclaration((yyval.interm).type, (yylsp[-2]), *(yyvsp[-2].lex).string, (yylsp[-1]), (yyvsp[0].interm.intermTypedNode));
    }

    break;

  case 124:

    {
        // $$.type is not used in invariant declarations.
        (yyval.interm).intermAggregate = context->parseInvariantDeclaration((yylsp[-1]), (yylsp[0]), (yyvsp[0].lex).string, (yyvsp[0].lex).symbol);
    }

    break;

  case 125:

    {
        (yyval.interm.type) = (yyvsp[0].interm.type);

        if ((yyvsp[0].interm.type).array) {
            ES3_ONLY("[]", (yylsp[0]), "first-class-array");
            if (context->getShaderVersion() != 300) {
                (yyvsp[0].interm.type).clearArrayness();
            }
        }
    }

    break;

  case 126:

    {
        (yyval.interm.type) = context->addFullySpecifiedType((yyvsp[-1].interm.type).qualifier, (yyvsp[-1].interm.type).invariant, (yyvsp[-1].interm.type).layoutQualifier, (yyvsp[0].interm.type));
    }

    break;

  case 127:

    {
        (yyval.interm.type).qualifier = EvqSmooth;
    }

    break;

  case 128:

    {
        (yyval.interm.type).qualifier = EvqFlat;
    }

    break;

  case 129:

    {
        (yyval.interm.qualifier) = EvqConst;
    }

    break;

  case 130:

    {
        VERTEX_ONLY("attribute", (yylsp[0]));
        ES2_ONLY("attribute", (yylsp[0]));
        if (context->globalErrorCheck((yylsp[0]), context->symbolTable.atGlobalLevel(), "attribute"))
            context->recover();
        (yyval.interm.type).setBasic(EbtVoid, EvqAttribute, (yylsp[0]));
    }

    break;

  case 131:

    {
        ES2_ONLY("varying", (yylsp[0]));
        if (context->globalErrorCheck((yylsp[0]), context->symbolTable.atGlobalLevel(), "varying"))
            context->recover();
        if (context->getShaderType() == GL_VERTEX_SHADER)
            (yyval.interm.type).setBasic(EbtVoid, EvqVaryingOut, (yylsp[0]));
        else
            (yyval.interm.type).setBasic(EbtVoid, EvqVaryingIn, (yylsp[0]));
    }

    break;

  case 132:

    {
        ES2_ONLY("varying", (yylsp[-1]));
        if (context->globalErrorCheck((yylsp[-1]), context->symbolTable.atGlobalLevel(), "invariant varying"))
            context->recover();
        if (context->getShaderType() == GL_VERTEX_SHADER)
            (yyval.interm.type).setBasic(EbtVoid, EvqVaryingOut, (yylsp[-1]));
        else
            (yyval.interm.type).setBasic(EbtVoid, EvqVaryingIn, (yylsp[-1]));
        (yyval.interm.type).invariant = true;
    }

    break;

  case 133:

    {
        if ((yyvsp[0].interm.type).qualifier != EvqConst && !context->symbolTable.atGlobalLevel())
        {
            context->error((yylsp[0]), "Local variables can only use the const storage qualifier.", getQualifierString((yyvsp[0].interm.type).qualifier));
            context->recover();
        }
        (yyval.interm.type).setBasic(EbtVoid, (yyvsp[0].interm.type).qualifier, (yylsp[0]));
    }

    break;

  case 134:

    {
        (yyval.interm.type) = context->joinInterpolationQualifiers((yylsp[-1]), (yyvsp[-1].interm.type).qualifier, (yylsp[0]), (yyvsp[0].interm.type).qualifier);
    }

    break;

  case 135:

    {
        context->error((yylsp[0]), "interpolation qualifier requires a fragment 'in' or vertex 'out' storage qualifier", getInterpolationString((yyvsp[0].interm.type).qualifier));
        context->recover();
        
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtVoid, qual, (yylsp[0]));
    }

    break;

  case 136:

    {
        (yyval.interm.type).qualifier = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).layoutQualifier = (yyvsp[0].interm.layoutQualifier);
    }

    break;

  case 137:

    {
        (yyval.interm.type).setBasic(EbtVoid, (yyvsp[0].interm.type).qualifier, (yylsp[0]));
        (yyval.interm.type).layoutQualifier = (yyvsp[-1].interm.layoutQualifier);
    }

    break;

  case 138:

    {
        context->es3InvariantErrorCheck((yyvsp[0].interm.type).qualifier, (yylsp[-1]));
        (yyval.interm.type).setBasic(EbtVoid, (yyvsp[0].interm.type).qualifier, (yylsp[0]));
        (yyval.interm.type).invariant = true;
    }

    break;

  case 139:

    {
        context->es3InvariantErrorCheck((yyvsp[0].interm.type).qualifier, (yylsp[-2]));
        (yyval.interm.type) = context->joinInterpolationQualifiers((yylsp[-1]), (yyvsp[-1].interm.type).qualifier, (yylsp[0]), (yyvsp[0].interm.type).qualifier);
        (yyval.interm.type).invariant = true;
    }

    break;

  case 140:

    {
        (yyval.interm.type).qualifier = EvqConst;
    }

    break;

  case 141:

    {
        ES3_ONLY("in", (yylsp[0]), "storage qualifier");
        (yyval.interm.type).qualifier = (context->getShaderType() == GL_FRAGMENT_SHADER) ? EvqFragmentIn : EvqVertexIn;
    }

    break;

  case 142:

    {
        ES3_ONLY("out", (yylsp[0]), "storage qualifier");
        (yyval.interm.type).qualifier = (context->getShaderType() == GL_FRAGMENT_SHADER) ? EvqFragmentOut : EvqVertexOut;
    }

    break;

  case 143:

    {
        ES3_ONLY("centroid in", (yylsp[-1]), "storage qualifier");
        if (context->getShaderType() == GL_VERTEX_SHADER)
        {
            context->error((yylsp[-1]), "invalid storage qualifier", "it is an error to use 'centroid in' in the vertex shader");
            context->recover();
        }
        (yyval.interm.type).qualifier = (context->getShaderType() == GL_FRAGMENT_SHADER) ? EvqCentroidIn : EvqVertexIn;
    }

    break;

  case 144:

    {
        ES3_ONLY("centroid out", (yylsp[-1]), "storage qualifier");
        if (context->getShaderType() == GL_FRAGMENT_SHADER)
        {
            context->error((yylsp[-1]), "invalid storage qualifier", "it is an error to use 'centroid out' in the fragment shader");
            context->recover();
        }
        (yyval.interm.type).qualifier = (context->getShaderType() == GL_FRAGMENT_SHADER) ? EvqFragmentOut : EvqCentroidOut;
    }

    break;

  case 145:

    {
        if (context->globalErrorCheck((yylsp[0]), context->symbolTable.atGlobalLevel(), "uniform"))
            context->recover();
        (yyval.interm.type).qualifier = EvqUniform;
    }

    break;

  case 146:

    {
        (yyval.interm.type) = (yyvsp[0].interm.type);

        if ((yyval.interm.type).precision == EbpUndefined) {
            (yyval.interm.type).precision = context->symbolTable.getDefaultPrecision((yyvsp[0].interm.type).type);
            if (context->precisionErrorCheck((yylsp[0]), (yyval.interm.type).precision, (yyvsp[0].interm.type).type)) {
                context->recover();
            }
        }
    }

    break;

  case 147:

    {
        (yyval.interm.type) = (yyvsp[0].interm.type);
        (yyval.interm.type).precision = (yyvsp[-1].interm.precision);

        if (!SupportsPrecision((yyvsp[0].interm.type).type)) {
            context->error((yylsp[-1]), "illegal type for precision qualifier", getBasicString((yyvsp[0].interm.type).type));
            context->recover();
        }
    }

    break;

  case 148:

    {
        (yyval.interm.precision) = EbpHigh;
    }

    break;

  case 149:

    {
        (yyval.interm.precision) = EbpMedium;
    }

    break;

  case 150:

    {
        (yyval.interm.precision) = EbpLow;
    }

    break;

  case 151:

    {
        ES3_ONLY("layout", (yylsp[-3]), "qualifier");
        (yyval.interm.layoutQualifier) = (yyvsp[-1].interm.layoutQualifier);
    }

    break;

  case 152:

    {
        (yyval.interm.layoutQualifier) = (yyvsp[0].interm.layoutQualifier);
    }

    break;

  case 153:

    {
        (yyval.interm.layoutQualifier) = context->joinLayoutQualifiers((yyvsp[-2].interm.layoutQualifier), (yyvsp[0].interm.layoutQualifier));
    }

    break;

  case 154:

    {
        (yyval.interm.layoutQualifier) = context->parseLayoutQualifier(*(yyvsp[0].lex).string, (yylsp[0]));
    }

    break;

  case 155:

    {
        (yyval.interm.layoutQualifier) = context->parseLayoutQualifier(*(yyvsp[-2].lex).string, (yylsp[-2]), *(yyvsp[0].lex).string, (yyvsp[0].lex).i, (yylsp[0]));
    }

    break;

  case 156:

    {
        (yyval.interm.layoutQualifier) = context->parseLayoutQualifier(*(yyvsp[-2].lex).string, (yylsp[-2]), *(yyvsp[0].lex).string, (yyvsp[0].lex).i, (yylsp[0]));
    }

    break;

  case 157:

    {
        (yyval.interm.type) = (yyvsp[0].interm.type);
    }

    break;

  case 158:

    {
        ES3_ONLY("[]", (yylsp[-1]), "implicitly sized array");
        (yyval.interm.type) = (yyvsp[-2].interm.type);
        (yyval.interm.type).setArraySize(0);
    }

    break;

  case 159:

    {
        (yyval.interm.type) = (yyvsp[-3].interm.type);

        if (context->arrayTypeErrorCheck((yylsp[-2]), (yyvsp[-3].interm.type)))
            context->recover();
        else {
            int size;
            if (context->arraySizeErrorCheck((yylsp[-2]), (yyvsp[-1].interm.intermTypedNode), size))
                context->recover();
            (yyval.interm.type).setArraySize(size);
        }
    }

    break;

  case 160:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtVoid, qual, (yylsp[0]));
    }

    break;

  case 161:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
    }

    break;

  case 162:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtInt, qual, (yylsp[0]));
    }

    break;

  case 163:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtUInt, qual, (yylsp[0]));
    }

    break;

  case 164:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtBool, qual, (yylsp[0]));
    }

    break;

  case 165:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(2);
    }

    break;

  case 166:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(3);
    }

    break;

  case 167:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(4);
    }

    break;

  case 168:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtBool, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(2);
    }

    break;

  case 169:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtBool, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(3);
    }

    break;

  case 170:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtBool, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(4);
    }

    break;

  case 171:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtInt, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(2);
    }

    break;

  case 172:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtInt, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(3);
    }

    break;

  case 173:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtInt, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(4);
    }

    break;

  case 174:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtUInt, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(2);
    }

    break;

  case 175:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtUInt, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(3);
    }

    break;

  case 176:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtUInt, qual, (yylsp[0]));
        (yyval.interm.type).setAggregate(4);
    }

    break;

  case 177:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setMatrix(2, 2);
    }

    break;

  case 178:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setMatrix(3, 3);
    }

    break;

  case 179:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setMatrix(4, 4);
    }

    break;

  case 180:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setMatrix(2, 3);
    }

    break;

  case 181:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setMatrix(3, 2);
    }

    break;

  case 182:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setMatrix(2, 4);
    }

    break;

  case 183:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setMatrix(4, 2);
    }

    break;

  case 184:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setMatrix(3, 4);
    }

    break;

  case 185:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtFloat, qual, (yylsp[0]));
        (yyval.interm.type).setMatrix(4, 3);
    }

    break;

  case 186:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtSampler2D, qual, (yylsp[0]));
    }

    break;

  case 187:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtSampler3D, qual, (yylsp[0]));
    }

    break;

  case 188:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtSamplerCube, qual, (yylsp[0]));
    }

    break;

  case 189:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtSampler2DArray, qual, (yylsp[0]));
    }

    break;

  case 190:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtISampler2D, qual, (yylsp[0]));
    }

    break;

  case 191:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtISampler3D, qual, (yylsp[0]));
    }

    break;

  case 192:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtISamplerCube, qual, (yylsp[0]));
    }

    break;

  case 193:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtISampler2DArray, qual, (yylsp[0]));
    }

    break;

  case 194:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtUSampler2D, qual, (yylsp[0]));
    }

    break;

  case 195:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtUSampler3D, qual, (yylsp[0]));
    }

    break;

  case 196:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtUSamplerCube, qual, (yylsp[0]));
    }

    break;

  case 197:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtUSampler2DArray, qual, (yylsp[0]));
    }

    break;

  case 198:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtSampler2DShadow, qual, (yylsp[0]));
    }

    break;

  case 199:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtSamplerCubeShadow, qual, (yylsp[0]));
    }

    break;

  case 200:

    {
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtSampler2DArrayShadow, qual, (yylsp[0]));
    }

    break;

  case 201:

    {
        if (!context->supportsExtension("GL_OES_EGL_image_external")) {
            context->error((yylsp[0]), "unsupported type", "samplerExternalOES");
            context->recover();
        }
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtSamplerExternalOES, qual, (yylsp[0]));
    }

    break;

  case 202:

    {
        if (!context->supportsExtension("GL_ARB_texture_rectangle")) {
            context->error((yylsp[0]), "unsupported type", "sampler2DRect");
            context->recover();
        }
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtSampler2DRect, qual, (yylsp[0]));
    }

    break;

  case 203:

    {
        (yyval.interm.type) = (yyvsp[0].interm.type);
        (yyval.interm.type).qualifier = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
    }

    break;

  case 204:

    {
        //
        // This is for user defined type names.  The lexical phase looked up the
        // type.
        //
        TType& structure = static_cast<TVariable*>((yyvsp[0].lex).symbol)->getType();
        TQualifier qual = context->symbolTable.atGlobalLevel() ? EvqGlobal : EvqTemporary;
        (yyval.interm.type).setBasic(EbtStruct, qual, (yylsp[0]));
        (yyval.interm.type).userDef = &structure;
    }

    break;

  case 205:

    { if (context->enterStructDeclaration((yylsp[-1]), *(yyvsp[-1].lex).string)) context->recover(); }

    break;

  case 206:

    {
        (yyval.interm.type) = context->addStructure((yylsp[-5]), (yylsp[-4]), (yyvsp[-4].lex).string, (yyvsp[-1].interm.fieldList));
    }

    break;

  case 207:

    { if (context->enterStructDeclaration((yylsp[0]), *(yyvsp[0].lex).string)) context->recover(); }

    break;

  case 208:

    {
        (yyval.interm.type) = context->addStructure((yylsp[-4]), (yyloc), NewPoolTString(""), (yyvsp[-1].interm.fieldList));
    }

    break;

  case 209:

    {
        (yyval.interm.fieldList) = (yyvsp[0].interm.fieldList);
    }

    break;

  case 210:

    {
        (yyval.interm.fieldList) = (yyvsp[-1].interm.fieldList);
        for (size_t i = 0; i < (yyvsp[0].interm.fieldList)->size(); ++i) {
            TField* field = (*(yyvsp[0].interm.fieldList))[i];
            for (size_t j = 0; j < (yyval.interm.fieldList)->size(); ++j) {
                if ((*(yyval.interm.fieldList))[j]->name() == field->name()) {
                    context->error((yylsp[0]), "duplicate field name in structure:", "struct", field->name().c_str());
                    context->recover();
                }
            }
            (yyval.interm.fieldList)->push_back(field);
        }
    }

    break;

  case 211:

    {
        (yyval.interm.fieldList) = context->addStructDeclaratorList((yyvsp[-2].interm.type), (yyvsp[-1].interm.fieldList));
    }

    break;

  case 212:

    {
        // ES3 Only, but errors should be handled elsewhere
        (yyvsp[-2].interm.type).qualifier = (yyvsp[-3].interm.type).qualifier;
        (yyvsp[-2].interm.type).layoutQualifier = (yyvsp[-3].interm.type).layoutQualifier;
        (yyval.interm.fieldList) = context->addStructDeclaratorList((yyvsp[-2].interm.type), (yyvsp[-1].interm.fieldList));
    }

    break;

  case 213:

    {
        (yyval.interm.fieldList) = NewPoolTFieldList();
        (yyval.interm.fieldList)->push_back((yyvsp[0].interm.field));
    }

    break;

  case 214:

    {
        (yyval.interm.fieldList)->push_back((yyvsp[0].interm.field));
    }

    break;

  case 215:

    {
        if (context->reservedErrorCheck((yylsp[0]), *(yyvsp[0].lex).string))
            context->recover();

        TType* type = new TType(EbtVoid, EbpUndefined);
        (yyval.interm.field) = new TField(type, (yyvsp[0].lex).string, (yylsp[0]));
    }

    break;

  case 216:

    {
        if (context->reservedErrorCheck((yylsp[-3]), *(yyvsp[-3].lex).string))
            context->recover();

        TType* type = new TType(EbtVoid, EbpUndefined);
        int size;
        if (context->arraySizeErrorCheck((yylsp[-1]), (yyvsp[-1].interm.intermTypedNode), size))
            context->recover();
        type->setArraySize(size);

        (yyval.interm.field) = new TField(type, (yyvsp[-3].lex).string, (yylsp[-3]));
    }

    break;

  case 217:

    { (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode); }

    break;

  case 218:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode); }

    break;

  case 219:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermAggregate); }

    break;

  case 220:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode); }

    break;

  case 221:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode); }

    break;

  case 222:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode); }

    break;

  case 223:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode); }

    break;

  case 224:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermSwitch); }

    break;

  case 225:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermCase); }

    break;

  case 226:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode); }

    break;

  case 227:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode); }

    break;

  case 228:

    { (yyval.interm.intermAggregate) = 0; }

    break;

  case 229:

    { context->symbolTable.push(); }

    break;

  case 230:

    { context->symbolTable.pop(); }

    break;

  case 231:

    {
        if ((yyvsp[-2].interm.intermAggregate) != 0) {
            (yyvsp[-2].interm.intermAggregate)->setOp(EOpSequence);
            (yyvsp[-2].interm.intermAggregate)->setLine((yyloc));
        }
        (yyval.interm.intermAggregate) = (yyvsp[-2].interm.intermAggregate);
    }

    break;

  case 232:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermAggregate); }

    break;

  case 233:

    { (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode); }

    break;

  case 234:

    { context->symbolTable.push(); }

    break;

  case 235:

    { context->symbolTable.pop(); (yyval.interm.intermNode) = (yyvsp[0].interm.intermAggregate); }

    break;

  case 236:

    { context->symbolTable.push(); }

    break;

  case 237:

    { context->symbolTable.pop(); (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode); }

    break;

  case 238:

    {
        (yyval.interm.intermAggregate) = 0;
    }

    break;

  case 239:

    {
        if ((yyvsp[-1].interm.intermAggregate)) {
            (yyvsp[-1].interm.intermAggregate)->setOp(EOpSequence);
            (yyvsp[-1].interm.intermAggregate)->setLine((yyloc));
        }
        (yyval.interm.intermAggregate) = (yyvsp[-1].interm.intermAggregate);
    }

    break;

  case 240:

    {
        (yyval.interm.intermAggregate) = context->intermediate.makeAggregate((yyvsp[0].interm.intermNode), (yyloc));
    }

    break;

  case 241:

    {
        (yyval.interm.intermAggregate) = context->intermediate.growAggregate((yyvsp[-1].interm.intermAggregate), (yyvsp[0].interm.intermNode), (yyloc));
    }

    break;

  case 242:

    { (yyval.interm.intermNode) = 0; }

    break;

  case 243:

    { (yyval.interm.intermNode) = static_cast<TIntermNode*>((yyvsp[-1].interm.intermTypedNode)); }

    break;

  case 244:

    {
        if (context->boolErrorCheck((yylsp[-4]), (yyvsp[-2].interm.intermTypedNode)))
            context->recover();
        (yyval.interm.intermNode) = context->intermediate.addSelection((yyvsp[-2].interm.intermTypedNode), (yyvsp[0].interm.nodePair), (yylsp[-4]));
    }

    break;

  case 245:

    {
        (yyval.interm.nodePair).node1 = (yyvsp[-2].interm.intermNode);
        (yyval.interm.nodePair).node2 = (yyvsp[0].interm.intermNode);
    }

    break;

  case 246:

    {
        (yyval.interm.nodePair).node1 = (yyvsp[0].interm.intermNode);
        (yyval.interm.nodePair).node2 = 0;
    }

    break;

  case 247:

    { context->incrSwitchNestingLevel(); }

    break;

  case 248:

    {
        (yyval.interm.intermSwitch) = context->addSwitch((yyvsp[-3].interm.intermTypedNode), (yyvsp[0].interm.intermAggregate), (yylsp[-5]));
        context->decrSwitchNestingLevel();
    }

    break;

  case 249:

    {
        (yyval.interm.intermCase) = context->addCase((yyvsp[-1].interm.intermTypedNode), (yylsp[-2]));
    }

    break;

  case 250:

    {
        (yyval.interm.intermCase) = context->addDefault((yylsp[-1]));
    }

    break;

  case 251:

    {
        (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode);
        if (context->boolErrorCheck((yyvsp[0].interm.intermTypedNode)->getLine(), (yyvsp[0].interm.intermTypedNode)))
            context->recover();
    }

    break;

  case 252:

    {
        TIntermNode *intermNode;
        if (context->boolErrorCheck((yylsp[-2]), (yyvsp[-3].interm.type)))
            context->recover();

        if (!context->executeInitializer((yylsp[-2]), *(yyvsp[-2].lex).string, (yyvsp[-3].interm.type), (yyvsp[0].interm.intermTypedNode), &intermNode))
            (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode);
        else {
            context->recover();
            (yyval.interm.intermTypedNode) = 0;
        }
    }

    break;

  case 253:

    { context->symbolTable.push(); context->incrLoopNestingLevel(); }

    break;

  case 254:

    {
        context->symbolTable.pop();
        (yyval.interm.intermNode) = context->intermediate.addLoop(ELoopWhile, 0, (yyvsp[-2].interm.intermTypedNode), 0, (yyvsp[0].interm.intermNode), (yylsp[-5]));
        context->decrLoopNestingLevel();
    }

    break;

  case 255:

    { context->incrLoopNestingLevel(); }

    break;

  case 256:

    {
        if (context->boolErrorCheck((yylsp[0]), (yyvsp[-2].interm.intermTypedNode)))
            context->recover();

        (yyval.interm.intermNode) = context->intermediate.addLoop(ELoopDoWhile, 0, (yyvsp[-2].interm.intermTypedNode), 0, (yyvsp[-5].interm.intermNode), (yylsp[-4]));
        context->decrLoopNestingLevel();
    }

    break;

  case 257:

    { context->symbolTable.push(); context->incrLoopNestingLevel(); }

    break;

  case 258:

    {
        context->symbolTable.pop();
        (yyval.interm.intermNode) = context->intermediate.addLoop(ELoopFor, (yyvsp[-3].interm.intermNode), reinterpret_cast<TIntermTyped*>((yyvsp[-2].interm.nodePair).node1), reinterpret_cast<TIntermTyped*>((yyvsp[-2].interm.nodePair).node2), (yyvsp[0].interm.intermNode), (yylsp[-6]));
        context->decrLoopNestingLevel();
    }

    break;

  case 259:

    {
        (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode);
    }

    break;

  case 260:

    {
        (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode);
    }

    break;

  case 261:

    {
        (yyval.interm.intermTypedNode) = (yyvsp[0].interm.intermTypedNode);
    }

    break;

  case 262:

    {
        (yyval.interm.intermTypedNode) = 0;
    }

    break;

  case 263:

    {
        (yyval.interm.nodePair).node1 = (yyvsp[-1].interm.intermTypedNode);
        (yyval.interm.nodePair).node2 = 0;
    }

    break;

  case 264:

    {
        (yyval.interm.nodePair).node1 = (yyvsp[-2].interm.intermTypedNode);
        (yyval.interm.nodePair).node2 = (yyvsp[0].interm.intermTypedNode);
    }

    break;

  case 265:

    {
        (yyval.interm.intermNode) = context->addBranch(EOpContinue, (yylsp[-1]));
    }

    break;

  case 266:

    {
        (yyval.interm.intermNode) = context->addBranch(EOpBreak, (yylsp[-1]));
    }

    break;

  case 267:

    {
        (yyval.interm.intermNode) = context->addBranch(EOpReturn, (yylsp[-1]));
    }

    break;

  case 268:

    {
        (yyval.interm.intermNode) = context->addBranch(EOpReturn, (yyvsp[-1].interm.intermTypedNode), (yylsp[-2]));
    }

    break;

  case 269:

    {
        FRAG_ONLY("discard", (yylsp[-1]));
        (yyval.interm.intermNode) = context->addBranch(EOpKill, (yylsp[-1]));
    }

    break;

  case 270:

    {
        (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode);
        context->setTreeRoot((yyval.interm.intermNode));
    }

    break;

  case 271:

    {
        (yyval.interm.intermNode) = context->intermediate.growAggregate((yyvsp[-1].interm.intermNode), (yyvsp[0].interm.intermNode), (yyloc));
        context->setTreeRoot((yyval.interm.intermNode));
    }

    break;

  case 272:

    {
        (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode);
    }

    break;

  case 273:

    {
        (yyval.interm.intermNode) = (yyvsp[0].interm.intermNode);
    }

    break;

  case 274:

    {
        context->parseFunctionPrototype((yylsp[0]), (yyvsp[0].interm).function, &(yyvsp[0].interm).intermAggregate);
    }

    break;

  case 275:

    {
        (yyval.interm.intermNode) = context->addFunctionDefinition(*((yyvsp[-2].interm).function), (yyvsp[-2].interm).intermAggregate, (yyvsp[0].interm.intermAggregate), (yylsp[-2]));
    }

    break;



      default: break;
    }
  /* User semantic actions sometimes alter yychar, and that requires
     that yytoken be updated with the new translation.  We take the
     approach of translating immediately before every use of yytoken.
     One alternative is translating here after every semantic action,
     but that translation would be missed if the semantic action invokes
     YYABORT, YYACCEPT, or YYERROR immediately after altering yychar or
     if it invokes YYBACKUP.  In the case of YYABORT or YYACCEPT, an
     incorrect destructor might then be invoked immediately.  In the
     case of YYERROR or YYBACKUP, subsequent parser actions might lead
     to an incorrect destructor call or verbose syntax error message
     before the lookahead is translated.  */
  YY_SYMBOL_PRINT ("-> $$ =", yyr1[yyn], &yyval, &yyloc);

  YYPOPSTACK (yylen);
  yylen = 0;
  YY_STACK_PRINT (yyss, yyssp);

  *++yyvsp = yyval;
  *++yylsp = yyloc;

  /* Now 'shift' the result of the reduction.  Determine what state
     that goes to, based on the state we popped back to and the rule
     number reduced by.  */

  yyn = yyr1[yyn];

  yystate = yypgoto[yyn - YYNTOKENS] + *yyssp;
  if (0 <= yystate && yystate <= YYLAST && yycheck[yystate] == *yyssp)
    yystate = yytable[yystate];
  else
    yystate = yydefgoto[yyn - YYNTOKENS];

  goto yynewstate;


/*--------------------------------------.
| yyerrlab -- here on detecting error.  |
`--------------------------------------*/
yyerrlab:
  /* Make sure we have latest lookahead translation.  See comments at
     user semantic actions for why this is necessary.  */
  yytoken = yychar == YYEMPTY ? YYEMPTY : YYTRANSLATE (yychar);

  /* If not already recovering from an error, report this error.  */
  if (!yyerrstatus)
    {
      ++yynerrs;
#if ! YYERROR_VERBOSE
      yyerror (&yylloc, context, scanner, YY_("syntax error"));
#else
# define YYSYNTAX_ERROR yysyntax_error (&yymsg_alloc, &yymsg, \
                                        yyssp, yytoken)
      {
        char const *yymsgp = YY_("syntax error");
        int yysyntax_error_status;
        yysyntax_error_status = YYSYNTAX_ERROR;
        if (yysyntax_error_status == 0)
          yymsgp = yymsg;
        else if (yysyntax_error_status == 1)
          {
            if (yymsg != yymsgbuf)
              YYSTACK_FREE (yymsg);
            yymsg = (char *) YYSTACK_ALLOC (yymsg_alloc);
            if (!yymsg)
              {
                yymsg = yymsgbuf;
                yymsg_alloc = sizeof yymsgbuf;
                yysyntax_error_status = 2;
              }
            else
              {
                yysyntax_error_status = YYSYNTAX_ERROR;
                yymsgp = yymsg;
              }
          }
        yyerror (&yylloc, context, scanner, yymsgp);
        if (yysyntax_error_status == 2)
          goto yyexhaustedlab;
      }
# undef YYSYNTAX_ERROR
#endif
    }

  yyerror_range[1] = yylloc;

  if (yyerrstatus == 3)
    {
      /* If just tried and failed to reuse lookahead token after an
         error, discard it.  */

      if (yychar <= YYEOF)
        {
          /* Return failure if at end of input.  */
          if (yychar == YYEOF)
            YYABORT;
        }
      else
        {
          yydestruct ("Error: discarding",
                      yytoken, &yylval, &yylloc, context, scanner);
          yychar = YYEMPTY;
        }
    }

  /* Else will try to reuse lookahead token after shifting the error
     token.  */
  goto yyerrlab1;


/*---------------------------------------------------.
| yyerrorlab -- error raised explicitly by YYERROR.  |
`---------------------------------------------------*/
yyerrorlab:

  /* Pacify compilers like GCC when the user code never invokes
     YYERROR and the label yyerrorlab therefore never appears in user
     code.  */
  if (/*CONSTCOND*/ 0)
     goto yyerrorlab;

  yyerror_range[1] = yylsp[1-yylen];
  /* Do not reclaim the symbols of the rule whose action triggered
     this YYERROR.  */
  YYPOPSTACK (yylen);
  yylen = 0;
  YY_STACK_PRINT (yyss, yyssp);
  yystate = *yyssp;
  goto yyerrlab1;


/*-------------------------------------------------------------.
| yyerrlab1 -- common code for both syntax error and YYERROR.  |
`-------------------------------------------------------------*/
yyerrlab1:
  yyerrstatus = 3;      /* Each real token shifted decrements this.  */

  for (;;)
    {
      yyn = yypact[yystate];
      if (!yypact_value_is_default (yyn))
        {
          yyn += YYTERROR;
          if (0 <= yyn && yyn <= YYLAST && yycheck[yyn] == YYTERROR)
            {
              yyn = yytable[yyn];
              if (0 < yyn)
                break;
            }
        }

      /* Pop the current state because it cannot handle the error token.  */
      if (yyssp == yyss)
        YYABORT;

      yyerror_range[1] = *yylsp;
      yydestruct ("Error: popping",
                  yystos[yystate], yyvsp, yylsp, context, scanner);
      YYPOPSTACK (1);
      yystate = *yyssp;
      YY_STACK_PRINT (yyss, yyssp);
    }

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END

  yyerror_range[2] = yylloc;
  /* Using YYLLOC is tempting, but would change the location of
     the lookahead.  YYLOC is available though.  */
  YYLLOC_DEFAULT (yyloc, yyerror_range, 2);
  *++yylsp = yyloc;

  /* Shift the error token.  */
  YY_SYMBOL_PRINT ("Shifting", yystos[yyn], yyvsp, yylsp);

  yystate = yyn;
  goto yynewstate;


/*-------------------------------------.
| yyacceptlab -- YYACCEPT comes here.  |
`-------------------------------------*/
yyacceptlab:
  yyresult = 0;
  goto yyreturn;

/*-----------------------------------.
| yyabortlab -- YYABORT comes here.  |
`-----------------------------------*/
yyabortlab:
  yyresult = 1;
  goto yyreturn;

#if !defined yyoverflow || YYERROR_VERBOSE
/*-------------------------------------------------.
| yyexhaustedlab -- memory exhaustion comes here.  |
`-------------------------------------------------*/
yyexhaustedlab:
  yyerror (&yylloc, context, scanner, YY_("memory exhausted"));
  yyresult = 2;
  /* Fall through.  */
#endif

yyreturn:
  if (yychar != YYEMPTY)
    {
      /* Make sure we have latest lookahead translation.  See comments at
         user semantic actions for why this is necessary.  */
      yytoken = YYTRANSLATE (yychar);
      yydestruct ("Cleanup: discarding lookahead",
                  yytoken, &yylval, &yylloc, context, scanner);
    }
  /* Do not reclaim the symbols of the rule whose action triggered
     this YYABORT or YYACCEPT.  */
  YYPOPSTACK (yylen);
  YY_STACK_PRINT (yyss, yyssp);
  while (yyssp != yyss)
    {
      yydestruct ("Cleanup: popping",
                  yystos[*yyssp], yyvsp, yylsp, context, scanner);
      YYPOPSTACK (1);
    }
#ifndef yyoverflow
  if (yyss != yyssa)
    YYSTACK_FREE (yyss);
#endif
#if YYERROR_VERBOSE
  if (yymsg != yymsgbuf)
    YYSTACK_FREE (yymsg);
#endif
  return yyresult;
}



int glslang_parse(TParseContext* context) {
    return yyparse(context, context->getScanner());
}
