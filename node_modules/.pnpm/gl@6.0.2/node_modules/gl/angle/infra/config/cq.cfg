# See http://luci-config.appspot.com/schemas/projects/refs:cq.cfg for the
# documentation of this file format.
version: 1
cq_name: "angle"
cq_status_url: "https://chromium-cq-status.appspot.com"
git_repo_url: "https://chromium.googlesource.com/angle/angle"
gerrit {
}
verifiers {
  try_job {
    buckets {
      name: "tryserver.chromium.angle"
      builders { name: "linux_angle_rel_ng" }
      builders { name: "linux_angle_dbg_ng" }
      builders { name: "mac_angle_rel_ng" }
      builders { name: "mac_angle_dbg_ng" }
      builders { name: "win_angle_rel_ng" }
      builders { name: "win_angle_dbg_ng" }
      builders { name: "win_angle_x64_rel_ng" }
      builders { name: "win_angle_x64_dbg_ng" }
    }
  }
}
