# ANGLE Development

ANGLE provides OpenGL ES 2.0 and EGL 1.4 libraries and dlls.  You can use these to build and run OpenGL ES 2.0 applications on Windows.

## Development setup

### Version Control
ANGLE uses git for version control. If you are not familiar with git, helpful documentation can be found at [http://git-scm.com/documentation](http://git-scm.com/documentation).

### Required Tools
 * [Visual Studio Community 2013](http://www.visualstudio.com/downloads/download-visual-studio-vs)
   * Required to build ANGLE on Windows and for the packaged Windows 8.1 SDK.
 * [depot_tools](http://dev.chromium.org/developers/how-tos/install-depot-tools)
   * Required to generate projects and build files, contribute patches, run the unit tests or build the shader compiler on non-Windows systems.
 * [Cygwin's Bison, flex, and patch](https://cygwin.com/setup-x86_64.exe) (optional)
   * This is only required if you need to modify GLSL ES grammar files (`glslang.l` and `glslang.y` under `src/compiler/translator`, or `ExpressionParser.y` and `Tokenizer.l` in `src/compiler/preprocessor`).
   * Use the latest versions of bison, flex and patch from the 64-bit cygwin distribution.

### Getting the source
Set the following environment variables as needed:

 * `GYP_GENERATORS` to `msvs` (other options include `ninja` and `make`)
 * `GYP_DEFINES` to `windows_sdk_path=YOUR_WIN_SDK_INSTALL_DIR` if you did not install the Windows 8.1 SDK in the default location.
 * `GYP_MSVS_VERSION` to `2013` (or `2013e` for Express editions of MSVS)

Download the ANGLE source by running the following commands:

```
git clone https://chromium.googlesource.com/angle/angle
cd angle
python scripts/bootstrap.py
gclient sync
git checkout master
```

Gyp will generate the main VS2013 solution file as build/ANGLE.sln. If you update ANGLE, or make a change to the projects, they can be regenerated by executing `gclient runhooks`. For generating a Windows Store version of ANGLE view the [Windows Store instructions](doc/BuildingAngleForWindowsStore.md).

### Building ANGLE
 1. Open one of the ANGLE Visual Studio solution files (see [Getting the source](DevSetup.md#Development-setup-Getting-the-source)).
 2. Select Build -> Configuration Manager
 3. In the "Active solution configuration:" drop down, select the desired configuration (eg. Release), and close the Configuration Manager.
 4. Select Build -> Build Solution.
Once the build completes, the output directory for your selected configuration (eg. `Release_Win32`, located next to the solution file) will contain the required libraries and dlls to build and run an OpenGL ES 2.0 application.

## Application Development with ANGLE
This sections describes how to use ANGLE to build an OpenGL ES application.

### Choosing a D3D Backend
ANGLE can use either a backing renderer which uses D3D11 on systems where it is available, or a D3D9-only renderer.

ANGLE provides an EGL extension called `EGL_ANGLE_platform_angle` which allows uers to select which renderer to use at EGL initialization time by calling eglGetPlatformDisplayEXT with special enums. Details of the extension can be found in it's specification in `extensions/ANGLE_platform_angle.txt` and `extensions/ANGLE_platform_angle_d3d.txt` and examples of it's use can be seen in the ANGLE samples and tests, particularly `util/EGLWindow.cpp`.

By default, ANGLE will use a D3D11 renderer. To change the default:

 1. Open `src/libANGLE/renderer/d3d/DisplayD3D.cpp`
 2. Locate the definition of `ANGLE_DEFAULT_D3D11` near the head of the file, and set it to your preference.

### To Use ANGLE in Your Application
 1. Configure your build environment to have access to the `include` folder to provide access to the standard Khronos EGL and GLES2 header files.
  * For Visual C++
     * Right-click your project in the _Solution Explorer_, and select _Properties_.
     * Under the _Configuration Properties_ branch, click _C/C++_.
     * Add the relative path to the Khronos EGL and GLES2 header files to _Additional Include Directories_.
 2. Configure your build environment to have access to `libEGL.lib` and `libGLESv2.lib` found in the build output directory (see [Building ANGLE](DevSteup.md#Building-ANGLE)).
   * For Visual C++
     * Right-click your project in the _Solution Explorer_, and select _Properties_.
     * Under the _Configuration Properties_ branch, open the _Linker_ branch and click _Input_.
     * Add the relative paths to both the `libEGL.lib` file and `libGLESv2.lib` file to _Additional Dependencies_, separated by a semicolon.
 3. Copy `libEGL.dll` and `libGLESv2.dll` from the build output directory (see [Building ANGLE](DevSetup.md#Building-ANGLE)) into your application folder.
 4. Code your application to the Khronos [OpenGL ES 2.0](http://www.khronos.org/registry/gles/) and [EGL 1.4](http://www.khronos.org/registry/egl/) APIs.

## GLSL ES to GLSL Translator
In addition to OpenGL ES 2.0 and EGL 1.4 libraries, ANGLE also provides a GLSL ES to GLSL translator. This is useful for implementing OpenGL ES emulators on top of desktop OpenGL.

### Getting the source
The translator code is fully independent of the rest of ANGLE code and resides in `src/compiler`.  It is cross-platform and build files for operating systems other than Windows can be generated by following the `Generating project files` steps above.

### Usage
The basic usage is shown in `essl_to_glsl` sample under `samples/translator`. To translate a GLSL ES shader, following functions need to be called in the same order:

 * `ShInitialize()` initializes the translator library and must be called only once from each process using the translator.
 * `ShContructCompiler()` creates a translator object for vertex or fragment shader.
 * `ShCompile()` translates the given shader.
 * `ShDestruct()` destroys the given translator.
 * `ShFinalize()` shuts down the translator library and must be called only once from each process using the translator.
