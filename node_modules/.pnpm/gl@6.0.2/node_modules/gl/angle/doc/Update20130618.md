# ANGLE Development Update - June 18, 2013

This week brings some significant changes to ANGLE, which we think are worth
covering in a development update.

## Migration from svn to git

We've changed our backing version control system from svn to git. Projects and
contributors pulling from the svn repository will continue to be able to do so,
but please note that this repository is now read-only, and no further updates
will be made there. To continue tracking new development and issue fixes, you'll
need to watch the git repository. Instructions on checking out code from the git
repository can be found on the [Source Checkout]
(https://code.google.com/p/angleproject/source/checkout) page.

## DirectX 11 Support

ANGLE now provides both a DirectX 9 and a DirectX 11-backed renderer in the same
code base. By default, support for the DirectX 11 renderer is disabled, but it
can be enabled by toggling the value of AN<PERSON><PERSON>\_ENABLE\_D3D11 as described on the
DevSetup page. On systems without DirectX 11 support, AN<PERSON><PERSON> will fall back to
DirectX 9.

This work originally appeared in our dx11proto branch, which, with the move to
the new repository, has been promoted to master. Code previously located in the
trunk of the svn repository will now be located in the git legacy branch, and
active development will now move to the newly promoted master.
