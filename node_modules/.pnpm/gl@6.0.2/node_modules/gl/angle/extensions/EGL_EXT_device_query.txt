Name

    EXT_device_query

Name Strings

    EGL_EXT_device_query

Contributors

    <PERSON>, <PERSON><PERSON><PERSON><PERSON>  (jajones 'at' nvidia.com)
    <PERSON>, Google  (jmadill 'at' google.com)

Contacts

    <PERSON>, <PERSON>  (jmadill 'at' google.com)

Status

    Draft

Version

    Version 1 - Mar 25rd, 2015

Number

    EGL Extension #XXX

Extension Type

    EGL client extension

Dependencies

    Written against the wording of EGL 1.5.

    Requires EGL 1.5 or an earlier verison of EGL with the
    EGL_EXT_client_extensions extension.

Overview

    Increasingly, EGL and its client APIs are being used in place of
    "native" rendering APIs to implement the basic graphics
    functionality of native windowing systems.  This creates demand
    for a method to access native GPU or device objects directly
    rather than calling EGL or GL entry points.

    This extension defines the method for an application to query
    native device objects from an EGL Display.

New Types

    This is the type of a handle that represents an EGLDeviceEXT
    object.

        typedef void* EGLDeviceEXT;

    If EGL 1.5 is not supported, the following type is added, as
    defined in the EGL 1.5 specification:

        typedef intptr_t EGLAttrib;

New Functions

    EGLBoolean eglQueryDeviceAttribEXT(EGLDeviceEXT device,
                                       EGLint attribute,
                                       EGLAttrib *value);

    const char *eglQueryDeviceStringEXT(EGLDeviceEXT device,
                                        EGLint name);

    EGLBoolean eglQueryDisplayAttribEXT(EGLDisplay dpy,
                                        EGLint attribute,
                                        EGLAttrib *value);

New Tokens

    Functions with a return type of EGLDeviceEXT will return this
    value on failure:

        EGL_NO_DEVICE_EXT                      ((EGLDeviceEXT)0)

    This error value will be generated by functions that take an
    EGLDeviceEXT object as a parameter:

        EGL_BAD_DEVICE_EXT                     0x322B

    Accepted by the <attribute> parameter of
    eglQueryDisplayAttribEXT:

        EGL_DEVICE_EXT                         0x322C

Add a new section "2.1.2 Devices" after "2.1.1 Scalar Types"

    All EGL operations occur on an EGLDeviceEXT.  However, devices
    themselves expose no functionality.  They are simple abstract
    objects that exist only for the sake of enumeration and
    defining a namespace.

Modify the last sentence of section "2.1.3" Displays" to read:

    Besides devices, objects are always specified by the combination
    of an EGLDisplay parameter with a parameter representing the
    handle of the object.

Add a new extension type to the list in section "2.8 Extensions"

    Device Extensions
        A *device extension* adds functionality to an individual
        EGLDeviceEXT.  Different instances of EGLDeviceEXT may support
        different sets of device extensions

Add a new error to section "3.1 Errors"

    EGL_BAD_DEVICE_EXT
        An EGLDeviceEXT argument does not refer to a valid
        EGLDeviceEXT.  Any command taking an EGLDeviceEXT parameter
        may generate this error.

Add a section "3.2 Devices" after "3.1 Errors"

    To query the properties of a device, use:

        EGLBoolean eglQueryDeviceAttribEXT(EGLDeviceEXT device,
                                           EGLint attribute,
                                           EGLAttrib *value);

    On success, EGL_TRUE is returned and the requested attribute value
    is returned in <value>.  Currently there are no valid values of
    <attribute> defined.

    On failure, EGL_FALSE is returned.  An EGL_BAD_ATTRIBUTE error is
    generated if <attribute> is not a valid attribute.  An
    EGL_BAD_DEVICE_EXT error is generated if <device> is not a valid
    EGLDeviceEXT.

        const char *eglQueryDeviceStringEXT(EGLDeviceEXT device,
                                            EGLint name);

    returns a pointer to a static, zero-terminated string describing
    some aspect of the specified EGLDeviceEXT.  <name> must be
    EGL_EXTENSIONS.

    The EGL_EXTENSIONS string describes which device extensions are
    supported by <device>.  The string is of the same format specified
    for display and client extension strings in section 3.4. Note that
    device extensions are properties of the device, and are distinct
    from other extension strings.

    On failure, NULL is returned.  An EGL_BAD_DEVICE_EXT error is
    generated if <device> is not a valid EGLDeviceEXT.  An
    EGL_BAD_PARAMETER error is generated if <name> is not one of the
    values described above.

Add a section "3.4 Display Attributes" after "3.3 EGL Versioning"

    To query attributes of an initialized display, use:

        EGLBoolean eglQueryDisplayAttribEXT(EGLDisplay dpy,
                                            EGLint name,
                                            EGLAttrib *value);

    On success, EGL_TRUE is returned.  If <name> is EGL_DEVICE_EXT,
    the EGLDeviceEXT associated with <dpy> is returned in <value>.
    All displays have an associated EGLDeviceEXT, regardless of how
    they were created.  A successful query of EGL_DEVICE_EXT will
    never return EGL_NO_DEVICE_EXT.

    On failure, EGL_FALSE is returned.  An EGL_NOT_INITIALIZED error
    is generated if EGL is not initialized for <dpy>.  An
    EGL_BAD_ATTRIBUTE error is generated if <name> is not a valid
    value.

    Because the EGLDeviceEXT is a property of <dpy>, any use of an
    associated EGLDeviceEXT after <dpy> has been terminated gives
    undefined results. Querying an EGL_DEVICE_EXT from <dpy> after a
    call to eglTerminate() (and subsequent re-initialization) may
    return a different value.

Issues

    None.

Revision History:

    #1  (Mar 25rd, 2015) Jamie Madill
        - Initial Draft based on EGL_EXT_device_base
