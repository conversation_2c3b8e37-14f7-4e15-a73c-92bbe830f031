Name

    ANGLE_texture_compression_dxt

Name Strings

    GL_ANGLE_texture_compression_dxt1
    GL_ANGLE_texture_compression_dxt3
    GL_ANGLE_texture_compression_dxt5

Contributors

    <PERSON>, Google Inc.
    Daniel <PERSON>, TransGaming Inc.
    Al <PERSON>, Google Inc.

Contacts

    <PERSON>, Google Inc. (gman 'at' google 'dot' com)

Status

    Implemented in ANGLE ES2

Version

    Last Modified Date: Sept 22, 2012
    Author Revision: 2

Number

    OpenGL ES Extension #111

Dependencies

    Requires OpenGL ES 1.0.

    The extension is written against the OpenGL ES 2.0 specification.

Overview

    These extensions are exactly the same as EXT_texture_compression_dxt1
    except they additionally expose the COMPRESSED_RGBA_S3TC_DXT3_ANGLE and
    COMPRESSED_RGBA_S3TC_DXT5_ANGLE formats and have a size restrictions
    such that the size must be a multiple of four (except for mip levels
    where the dimensions are either 2 or 1).

    See EXT_texture_compression_dxt1 for the full list of changes. Also
    see EXT_texture_compression_s3tc for a description of the formats.

IP Status

    A license to the S3TC Intellectual Property may be necessary for 
    implementation of this extension.  You should consult with your 
    Attorney to determine the need for a license.

New Procedures and Functions

    None.

New Types

    None.

New Tokens

    Accepted by the <internalformat> parameter of CompressedTexImage2D
    and the <format> parameter of CompressedTexSubImage2D:

    COMPRESSED_RGB_S3TC_DXT1_ANGLE                    0x83F0
    COMPRESSED_RGBA_S3TC_DXT1_ANGLE                   0x83F1
    COMPRESSED_RGBA_S3TC_DXT3_ANGLE                   0x83F2
    COMPRESSED_RGBA_S3TC_DXT5_ANGLE                   0x83F3

Additions to Chapter 3 of the OpenGL ES 2.0 Specification (Rasterization)

    Add the following to Section 3.7.3 (Compressed Texture Images)
    (at the end of the description of the CompressedTexImage2D command):

    Compressed Internal Format        Base Internal Format
    ==========================        ====================
    COMPRESSED_RGB_S3TC_DXT1_ANGLE    RGB
    COMPRESSED_RGBA_S3TC_DXT1_ANGLE   RGBA
    COMPRESSED_RGBA_S3TC_DXT3_ANGLE   RGBA
    COMPRESSED_RGBA_S3TC_DXT5_ANGLE   RGBA

    Table 3.x: Specific Compressed Internal Formats

    If <internalformat> is one of the S3TC formats listed in Table 3.x,
    the compressed texture is stored in the appropriate S3TC compressed
    texture format (see Appendix). The GL and the S3TC texture compression
    algorithm support only 2D images without borders.

    CompressedTexImage2D will produce the INVALID_OPERATION error when
    <internalformat> is one of the values from Table 3.x under the following
    conditions: 

      * <border> is non-zero.
      * <width> is not one, two, or a multiple of four.
      * <height> is not one, two, or a multiple of four.

    Add the following to Section 3.7.3 (Compressed Texture Images)
    (at the end of the description of the CompressedTexSubImage2D command):

    If the internal format of the texture image being modified is listed
    in Table 3.x, the texture is stored in the appropriate S3TC compressed
    texture format (see Appendix). Since DXT/S3TC images are easily edited
    along 4x4 texel boundaries, the limitations of CompressedTexSubImage2D
    are relaxed. CompressedTexSubImage2D will result in an INVALID_OPERATION
    error only if one of the following conditions occurs:

      * <width> is not a multiple of four or equal to TEXTURE_WIDTH.
      * <height> is not a multipls of four or equal to TEXTURE_HEIGHT.
      * <xoffset> or <yoffset> is not a multiple of four.
      * <format> does not match the internal format of the texture image
        being modified.

    The following restrictions at the end of section 3.7.3 do not apply
    to S3TC DXT texture formats, since subimage modification is straightforward
    as long as the subimage is properly aligned.

Errors

    INVALID_OPERATION is generated by CompressedTexImage2D if <internalformat>
    is one of the compressed internal formats from Table 3.x and any of the 
    following apply:
      - <border> is not equal to zero.
      - <width> is not one, two, or a multiple of four. 
      - <height> is not one, two, or a multiple of four. 

    INVALID_OPERATION is generated by TexImage2D and CopyTexImage2D if
    <internalformat> is one of the compressed internal formats from
    Table 3.x.

    INVALID_OPERATION is generated by TexSubImage2D and CopyTexSubImage2D
    if the internal format of the texture currently bound to <target> is
    one of the compressed internal formats from Table 3.x.

    INVALID_OPERATION is generated by CompressedTexSubImage2D if <format>
    is one of the compressed interal formats from Table 3.x and any of the
    following apply:
      - <width> is not a multiple of four or equal to TEXTURE_WIDTH;
      - <height> is not a multiple of four or equal to TEXTURE_HEIGHT;
      - <xoffset> or <yoffset> is not a multiple of four;
      - <format> does not match the internal format of the texture image
      being modified.

New State

    None.

Appendix: 

    The format for the S3TC Compressed Texture Images Formats is documented
    in the appendix of EXT_texture_compression_s3tc.

Revision History

    Revision 1, 2010/08/06 - gman
      - Initial revision
    Revision 2, 2012/09/22 - dgkoch
      - Added DXT1 formats and documented multiple of 4 restriction.

