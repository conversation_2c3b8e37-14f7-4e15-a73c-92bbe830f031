#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11_encoding@0.1.13/node_modules/@mapbox/node-pre-gyp/bin/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11_encoding@0.1.13/node_modules/@mapbox/node-pre-gyp/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11_encoding@0.1.13/node_modules/@mapbox/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11_encoding@0.1.13/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11_encoding@0.1.13/node_modules/@mapbox/node-pre-gyp/bin/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11_encoding@0.1.13/node_modules/@mapbox/node-pre-gyp/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11_encoding@0.1.13/node_modules/@mapbox/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11_encoding@0.1.13/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../@mapbox+node-pre-gyp@1.0.11_encoding@0.1.13/node_modules/@mapbox/node-pre-gyp/bin/node-pre-gyp" "$@"
else
  exec node  "$basedir/../../../../../@mapbox+node-pre-gyp@1.0.11_encoding@0.1.13/node_modules/@mapbox/node-pre-gyp/bin/node-pre-gyp" "$@"
fi
