to<PERSON>uffer and other canvas methods were static and unwrapped
the this arg. this seemed weird. they are now instance
methods since that's how object wrap works, XXX add
instanceof checks?

node-canvas no lunger flushes microtasks when calling stream
cbs XXX wait now that I say it that sounds wrong?

we don't need CHECK_RECEIVER anymore? dbl check that?

TODO: check changes from As<X> to AsX. bad assumption.

dbl check try catch changes?

Error is now CairoError

global this in all callbacks, streamPNGSync now flushes microtasks

streamPDF uses MakeCallback, not runInAsyncScope
