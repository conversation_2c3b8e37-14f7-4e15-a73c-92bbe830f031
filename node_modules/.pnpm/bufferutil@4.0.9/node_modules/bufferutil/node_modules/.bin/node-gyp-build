#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/node-gyp-build@4.8.4/node_modules/node-gyp-build/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/node-gyp-build@4.8.4/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/node-gyp-build@4.8.4/node_modules/node-gyp-build/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/node-gyp-build@4.8.4/node_modules:/Users/<USER>/Documents/Projects/sol_wallet_gen/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../node-gyp-build@4.8.4/node_modules/node-gyp-build/bin.js" "$@"
else
  exec node  "$basedir/../../../../../node-gyp-build@4.8.4/node_modules/node-gyp-build/bin.js" "$@"
fi
